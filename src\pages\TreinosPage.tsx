import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  Avatar,
  Chip,
  Stack
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import { Cliente } from '../models/Cliente';
import { Treino } from '../models/Treino';
import { Exercicio } from '../models/Exercicio';
import { Serie } from '../models/Serie';
import { useAppContext } from '../contexts/AppContext';
import { useTreino } from '../hooks/useTreino';
import { CardSkeleton } from '../components/common/LoadingSkeletons';
import { colors } from '../styles/colors';

const TreinosPage: React.FC = () => {
  const { clientes, clienteSelecionado, selecionarCliente } = useAppContext();
  const { 
    treinos, 
    treinoSelecionado, 
    exercicios,
    carregando, 
    erro,
    carregarTreinosPorCliente,
    carregarExerciciosPorTreino,
    criarTreino,
    excluirTreino,
    selecionarTreino,
    criarExercicio,
    excluirExercicio,
    criarSerie,
    obterSeriesPorExercicio
  } = useTreino();
  
  const [dialogClienteAberto, setDialogClienteAberto] = useState(false);
  const [dialogTreinoAberto, setDialogTreinoAberto] = useState(false);
  const [dialogExercicioAberto, setDialogExercicioAberto] = useState(false);
  const [dialogSerieAberto, setDialogSerieAberto] = useState(false);
  
  const [tipoTreinoAtivo, setTipoTreinoAtivo] = useState<string>('A');
  const [termoBusca, setTermoBusca] = useState('');
  const [seriesPorExercicio, setSeriesPorExercicio] = useState<{[key: number]: Serie[]}>({});
  
  const [novoTreino, setNovoTreino] = useState<Partial<Treino>>({
    nome: '',
    data_inicio: new Date().toISOString().split('T')[0],
    observacoes: '',
    data_criacao: new Date().toISOString().split('T')[0]
  });
  
  const [novoExercicio, setNovoExercicio] = useState<Partial<Exercicio>>({
    nome: '',
    tipo_treino: 'A',
    ordem: 1
  });
  
  const [novaSerie, setNovaSerie] = useState<Partial<Serie>>({
    semana: 1,
    numero_serie: 1,
    repeticoes: 12,
    carga: 10
  });
  
  const [itemEditando, setItemEditando] = useState<number | null>(null);
  const [semanaAtiva, setSemanaAtiva] = useState<number>(1);

  // Carregar treinos quando o cliente for selecionado
  useEffect(() => {
    if (clienteSelecionado) {
      carregarTreinosPorCliente(clienteSelecionado.id!);
    }
  }, [clienteSelecionado, carregarTreinosPorCliente]);

  // Carregar exercícios quando o treino for selecionado
  useEffect(() => {
    if (treinoSelecionado) {
      carregarExerciciosPorTreino(treinoSelecionado.id!, tipoTreinoAtivo);
    }
  }, [treinoSelecionado, tipoTreinoAtivo, carregarExerciciosPorTreino]);

  // Carregar séries para cada exercício
  useEffect(() => {
    const carregarSeries = async () => {
      if (exercicios.length > 0) {
        const seriesTemp: {[key: number]: Serie[]} = {};
        
        for (const exercicio of exercicios) {
          try {
            const series = await obterSeriesPorExercicio(exercicio.id!, semanaAtiva);
            seriesTemp[exercicio.id!] = series;
          } catch (error) {
            console.error(`Erro ao carregar séries para o exercício ${exercicio.id}:`, error);
          }
        }
        
        setSeriesPorExercicio(seriesTemp);
      }
    };
    
    carregarSeries();
  }, [exercicios, semanaAtiva, obterSeriesPorExercicio]);

  const handleSelecionarCliente = (cliente: Cliente) => {
    selecionarCliente(cliente);
    setDialogClienteAberto(false);
  };

  const handleCriarTreino = async () => {
    if (!clienteSelecionado) {
      return;
    }

    try {
      const novoTreinoCompleto: Treino = {
        ...novoTreino as Treino,
        cliente_id: clienteSelecionado.id!,
        data_criacao: new Date().toISOString().split('T')[0]
      };
      
      await criarTreino(novoTreinoCompleto);
      
      // Limpar formulário
      setNovoTreino({
        nome: '',
        data_inicio: new Date().toISOString().split('T')[0],
        observacoes: '',
        data_criacao: new Date().toISOString().split('T')[0]
      });
      
      setDialogTreinoAberto(false);
    } catch (error) {
      console.error('Erro ao criar treino:', error);
    }
  };

  const handleCriarExercicio = async () => {
    if (!treinoSelecionado) {
      return;
    }

    try {
      const novoExercicioCompleto: Exercicio = {
        ...novoExercicio as Exercicio,
        treino_id: treinoSelecionado.id!,
        tipo_treino: tipoTreinoAtivo
      };
      
      await criarExercicio(novoExercicioCompleto);
      
      // Limpar formulário
      setNovoExercicio({
        nome: '',
        tipo_treino: tipoTreinoAtivo,
        ordem: exercicios.length + 1
      });
      
      setDialogExercicioAberto(false);
    } catch (error) {
      console.error('Erro ao criar exercício:', error);
    }
  };

  const handleCriarSerie = async (exercicioId: number) => {
    try {
      const novaSerieCompleta: Serie = {
        ...novaSerie as Serie,
        exercicio_id: exercicioId,
        volume_carga: novaSerie.repeticoes! * novaSerie.carga!
      };
      
      await criarSerie(novaSerieCompleta);
      
      // Recarregar séries
      const series = await obterSeriesPorExercicio(exercicioId, semanaAtiva);
      setSeriesPorExercicio({
        ...seriesPorExercicio,
        [exercicioId]: series
      });
      
      // Limpar formulário
      setNovaSerie({
        semana: semanaAtiva,
        numero_serie: 1,
        repeticoes: 12,
        carga: 10
      });
      
      setDialogSerieAberto(false);
      setItemEditando(null);
    } catch (error) {
      console.error('Erro ao criar série:', error);
    }
  };

  const handleExcluirTreino = async (id: number) => {
    try {
      await excluirTreino(id);
    } catch (error) {
      console.error('Erro ao excluir treino:', error);
    }
  };

  const handleExcluirExercicio = async (id: number) => {
    try {
      await excluirExercicio(id);
    } catch (error) {
      console.error('Erro ao excluir exercício:', error);
    }
  };

  const tiposTreino = ['A', 'B', 'C', 'D', 'E', 'F'];
  const semanas = [1, 2, 3, 4, 5, 6, 7, 8];

  return (
    <Box>
      <Box sx={{ 
        mb: 4, 
        p: 3, 
        borderRadius: 4, 
        background: `${colors.sea}0A`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Typography variant="h4" fontWeight="bold" sx={{ color: colors.ocean }}>
          Gerenciamento de Treinos
        </Typography>
        <Stack direction="row" spacing={2}>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleCriarTreino}
            disabled={!clienteSelecionado}
            sx={{ 
              bgcolor: colors.sea,
              borderRadius: 3,
              py: 1.2,
              px: 3,
              '&:hover': {
                bgcolor: colors.ocean,
                boxShadow: `0px 4px 8px ${colors.sea}33`
              },
              '&.Mui-disabled': {
                bgcolor: `${colors.sea}4D`,
                color: 'white'
              }
            }}
          >
            Novo Treino
          </Button>
        </Stack>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Paper 
            elevation={0} 
            sx={{ 
              p: 3, 
              borderRadius: 4,
              boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
              bgcolor: colors.cloud
            }}
          >
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, color: colors.ocean }}>
              Selecionar Cliente
            </Typography>
            <FormControl fullWidth>
              <InputLabel id="cliente-select-label" sx={{ color: `${colors.ocean}99` }}>
                Cliente
              </InputLabel>
              <Select
                labelId="cliente-select-label"
                value={clienteSelecionado?.id || ''}
                onChange={(e) => {
                  const cliente = clientes.find(c => c.id === e.target.value);
                  if (cliente) handleSelecionarCliente(cliente);
                }}
                sx={{
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: colors.sea,
                  }
                }}
              >
                {clientes.map((cliente) => (
                  <MenuItem key={cliente.id} value={cliente.id}>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Avatar 
                        sx={{ 
                          width: 24, 
                          height: 24, 
                          bgcolor: `${colors.sea}1A`,
                          color: colors.sea,
                          fontSize: '0.8rem'
                        }}
                      >
                        {cliente.nome.charAt(0).toUpperCase()}
                      </Avatar>
                      <Typography>{cliente.nome}</Typography>
                    </Stack>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          {clienteSelecionado ? (
            carregando ? (
              <CardSkeleton rows={3} animated={true} />
            ) : treinos.length > 0 ? (
              <Box>
                {treinos.map((treino) => (
                  <Paper 
                    key={treino.id}
                    elevation={0}
                    sx={{ 
                      p: 3, 
                      mb: 3,
                      borderRadius: 4,
                      boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
                      bgcolor: colors.cloud
                    }}
                  >
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      mb: 2
                    }}>
                      <Stack direction="row" alignItems="center" spacing={2}>
                        <Box sx={{ 
                          bgcolor: `${colors.sea}1A`, 
                          p: 1, 
                          borderRadius: '50%',
                          display: 'flex'
                        }}>
                          <FitnessCenterIcon sx={{ color: colors.sea }} />
                        </Box>
                        <Box>
                          <Typography variant="h6" fontWeight="bold" sx={{ color: colors.ocean }}>
                            {treino.nome}
                          </Typography>
                          <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                            Criado em: {new Date(treino.data_criacao).toLocaleDateString()}
                          </Typography>
                        </Box>
                      </Stack>
                      <Stack direction="row" spacing={1}>
                        <Tooltip title="Editar Treino" arrow>
                          <IconButton 
                            size="small"
                            sx={{ 
                              color: colors.sea,
                              '&:hover': { 
                                bgcolor: `${colors.sea}1A` 
                              }
                            }}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Excluir Treino" arrow>
                          <IconButton 
                            size="small"
                            onClick={() => handleExcluirTreino(treino.id!)}
                            sx={{ 
                              color: '#FF4D4D',
                              '&:hover': { 
                                bgcolor: 'rgba(255, 77, 77, 0.1)' 
                              }
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </Box>

                    {/* Continue with existing exercicios and series rendering... */}
                    
                  </Paper>
                ))}
              </Box>
            ) : (
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: 300,
                p: 3,
                bgcolor: colors.cloud,
                borderRadius: 4
              }}>
                <Box sx={{ 
                  bgcolor: `${colors.sea}1A`, 
                  p: 2, 
                  borderRadius: '50%',
                  mb: 2
                }}>
                  <FitnessCenterIcon sx={{ fontSize: 40, color: colors.sea }} />
                </Box>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 500, color: colors.ocean }}>
                  Nenhum treino encontrado
                </Typography>
                <Typography variant="body2" sx={{ color: `${colors.ocean}99`, mb: 3, textAlign: 'center' }}>
                  Crie um novo treino para começar a montar o programa de exercícios
                </Typography>
                <Button 
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCriarTreino}
                  sx={{ 
                    bgcolor: colors.sea,
                    borderRadius: 3,
                    py: 1.2,
                    px: 3,
                    '&:hover': {
                      bgcolor: colors.ocean,
                      boxShadow: `0px 4px 8px ${colors.sea}33`
                    }
                  }}
                >
                  Criar Primeiro Treino
                </Button>
              </Box>
            )
          ) : (
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: 300,
              p: 3,
              bgcolor: colors.cloud,
              borderRadius: 4
            }}>
              <Typography variant="h6" sx={{ mb: 1, fontWeight: 500, color: colors.ocean }}>
                Selecione um cliente para visualizar ou criar treinos
              </Typography>
            </Box>
          )}
        </Grid>
      </Grid>

      {/* Dialog para criar novo treino */}
      <Dialog 
        open={dialogTreinoAberto} 
        onClose={() => setDialogTreinoAberto(false)}
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <DialogTitle sx={{ color: colors.ocean, fontWeight: 600 }}>
          Novo Treino
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Nome do Treino"
              variant="outlined"
              value={novoTreino.nome}
              onChange={(e) => setNovoTreino({ ...novoTreino, nome: e.target.value })}
              margin="normal"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
            
            <TextField
              fullWidth
              label="Data de Início"
              type="date"
              variant="outlined"
              value={novoTreino.data_inicio}
              onChange={(e) => setNovoTreino({ ...novoTreino, data_inicio: e.target.value })}
              margin="normal"
              InputLabelProps={{ shrink: true }}
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
            
            <TextField
              fullWidth
              label="Data de Término"
              type="date"
              variant="outlined"
              value={novoTreino.data_fim || ''}
              onChange={(e) => setNovoTreino({ ...novoTreino, data_fim: e.target.value })}
              margin="normal"
              InputLabelProps={{ shrink: true }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
            
            <TextField
              fullWidth
              label="Observações"
              variant="outlined"
              value={novoTreino.observacoes || ''}
              onChange={(e) => setNovoTreino({ ...novoTreino, observacoes: e.target.value })}
              margin="normal"
              multiline
              rows={3}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button 
            onClick={() => setDialogTreinoAberto(false)}
            sx={{ 
              color: `${colors.ocean}99`,
              '&:hover': {
                bgcolor: `${colors.ocean}0A`,
                color: colors.ocean
              }
            }}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleCriarTreino}
            variant="contained"
            disabled={!novoTreino.nome || !novoTreino.data_inicio}
            sx={{ 
              bgcolor: colors.sea,
              '&:hover': {
                bgcolor: colors.ocean,
                boxShadow: `0px 4px 8px ${colors.sea}33`
              },
              '&.Mui-disabled': {
                bgcolor: `${colors.sea}4D`,
                color: 'white'
              }
            }}
          >
            Criar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para criar novo exercício */}
      <Dialog 
        open={dialogExercicioAberto} 
        onClose={() => setDialogExercicioAberto(false)}
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <DialogTitle sx={{ color: colors.ocean, fontWeight: 600 }}>
          Novo Exercício
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Nome do Exercício"
              variant="outlined"
              value={novoExercicio.nome}
              onChange={(e) => setNovoExercicio({ ...novoExercicio, nome: e.target.value })}
              margin="normal"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
            
            <TextField
              fullWidth
              select
              label="Tipo de Treino"
              variant="outlined"
              value={novoExercicio.tipo_treino}
              onChange={(e) => setNovoExercicio({ ...novoExercicio, tipo_treino: e.target.value })}
              margin="normal"
              required
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            >
              {tiposTreino.map((tipo) => (
                <MenuItem key={tipo} value={tipo}>
                  Treino {tipo}
                </MenuItem>
              ))}
            </TextField>
            
            <TextField
              fullWidth
              label="Ordem"
              type="number"
              variant="outlined"
              value={novoExercicio.ordem}
              onChange={(e) => setNovoExercicio({ ...novoExercicio, ordem: parseInt(e.target.value) })}
              margin="normal"
              required
              InputProps={{ inputProps: { min: 1 } }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button 
            onClick={() => setDialogExercicioAberto(false)}
            sx={{ 
              color: `${colors.ocean}99`,
              '&:hover': {
                bgcolor: `${colors.ocean}0A`,
                color: colors.ocean
              }
            }}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleCriarExercicio}
            variant="contained"
            disabled={!novoExercicio.nome || !novoExercicio.tipo_treino}
            sx={{ 
              bgcolor: colors.sea,
              '&:hover': {
                bgcolor: colors.ocean,
                boxShadow: `0px 4px 8px ${colors.sea}33`
              },
              '&.Mui-disabled': {
                bgcolor: `${colors.sea}4D`,
                color: 'white'
              }
            }}
          >
            Criar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para criar nova série */}
      <Dialog 
        open={dialogSerieAberto} 
        onClose={() => setDialogSerieAberto(false)}
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <DialogTitle sx={{ color: colors.ocean, fontWeight: 600 }}>
          Nova Série
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Semana"
              type="number"
              variant="outlined"
              value={novaSerie.semana}
              onChange={(e) => setNovaSerie({ ...novaSerie, semana: parseInt(e.target.value) })}
              margin="normal"
              required
              InputProps={{ inputProps: { min: 1, max: 8 } }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
            
            <TextField
              fullWidth
              label="Número da Série"
              type="number"
              variant="outlined"
              value={novaSerie.numero_serie}
              onChange={(e) => setNovaSerie({ ...novaSerie, numero_serie: parseInt(e.target.value) })}
              margin="normal"
              required
              InputProps={{ inputProps: { min: 1 } }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
            
            <TextField
              fullWidth
              label="Repetições"
              type="number"
              variant="outlined"
              value={novaSerie.repeticoes}
              onChange={(e) => setNovaSerie({ ...novaSerie, repeticoes: parseInt(e.target.value) })}
              margin="normal"
              required
              InputProps={{ inputProps: { min: 1 } }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
            
            <TextField
              fullWidth
              label="Carga (kg)"
              type="number"
              variant="outlined"
              value={novaSerie.carga}
              onChange={(e) => setNovaSerie({ ...novaSerie, carga: parseFloat(e.target.value) })}
              margin="normal"
              required
              InputProps={{ inputProps: { min: 0, step: 0.5 } }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: `${colors.ocean}1A`,
                  },
                  '&:hover fieldset': {
                    borderColor: `${colors.sea}4D`,
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: colors.sea,
                  }
                }
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button 
            onClick={() => {
              setDialogSerieAberto(false);
              setItemEditando(null);
            }}
            sx={{ 
              color: `${colors.ocean}99`,
              '&:hover': {
                bgcolor: `${colors.ocean}0A`,
                color: colors.ocean
              }
            }}
          >
            Cancelar
          </Button>
          <Button 
            onClick={() => handleCriarSerie(itemEditando!)}
            variant="contained"
            disabled={!itemEditando || !novaSerie.repeticoes || !novaSerie.carga}
            sx={{ 
              bgcolor: colors.sea,
              '&:hover': {
                bgcolor: colors.ocean,
                boxShadow: `0px 4px 8px ${colors.sea}33`
              },
              '&.Mui-disabled': {
                bgcolor: `${colors.sea}4D`,
                color: 'white'
              }
            }}
          >
            Criar
          </Button>
        </DialogActions>
      </Dialog>

    </Box>
  );
};

export default TreinosPage;