{"name": "personal-trainer-app", "version": "1.0.0", "description": "Aplicativo para personal trainers gerenciarem clientes, avaliações físicas e treinos", "main": "electron.js", "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject", "electron-dev": "wait-on http://localhost:3000 && electron .", "electron-dev-debug": "wait-on http://localhost:3000 --timeout 30000 --verbose && electron .", "dev": "concurrently \"yarn start\" \"yarn electron-delayed\"", "electron-delayed": "node start-electron-delayed.js", "dev-safe": "concurrently \"yarn start\" \"yarn electron-dev-debug\"", "dev-simple": "concurrently \"yarn start\" \"timeout 15 && npx electron .\"", "pack": "electron-builder --dir", "dist": "electron-builder"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.19", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/jspdf": "^2.0.0", "@types/node": "^16.18.65", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "assert": "^2.1.0", "axios": "^0.21.1", "better-sqlite3": "^8.7.0", "browserify-zlib": "^0.2.0", "chart.js": "^4.4.0", "electron-is-dev": "^2.0.0", "jspdf": "^3.0.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "recharts": "^2.15.1", "stream-browserify": "^3.0.0", "typescript": "^4.9.5", "util": "^0.12.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/better-sqlite3": "^7.6.8", "browserify-fs": "^1.0.0", "buffer": "^6.0.3", "concurrently": "^8.2.2", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "customize-cra": "^1.0.0", "electron": "^27.1.2", "electron-builder": "^24.9.1", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "stream-http": "^3.2.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "url": "^0.11.4", "wait-on": "^7.2.0", "webpack-dev-server": "^5.2.0"}, "build": {"appId": "com.personaltrainer.app", "productName": "Personal Trainer App", "files": ["build/**/*", "electron.js", "node_modules/**/*"], "directories": {"buildResources": "assets"}, "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}