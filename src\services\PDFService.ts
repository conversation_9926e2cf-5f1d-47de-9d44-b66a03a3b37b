import jsPDF from 'jspdf';
// import html2canvas from 'html2canvas'; // Removido temporariamente para evitar conflitos
import { AvaliacaoFisica } from '../models/AvaliacaoFisica';
import { Cliente } from '../models/Cliente';
import { Treino, Exercicio, Serie } from '../models/Treino';
import { formatarData } from '../utils/formatadores';
// import { colors } from '../styles/colors'; // Removido temporariamente

// ============================================================================
// PDF SERVICE - GERAÇÃO DE RELATÓRIOS PROFISSIONAIS
// ============================================================================

export class PDFService {
  private static readonly MARGIN = 20;
  private static readonly PAGE_WIDTH = 210; // A4 width in mm
  private static readonly PAGE_HEIGHT = 297; // A4 height in mm
  private static readonly CONTENT_WIDTH = PDFService.PAGE_WIDTH - (PDFService.MARGIN * 2);

  // --------------------------------------------------------------------------
  // RELATÓRIO DE AVALIAÇÃO FÍSICA COMPLETO
  // --------------------------------------------------------------------------
  static async gerarRelatorioAvaliacaoFisica(
    avaliacao: AvaliacaoFisica, 
    cliente: Cliente
  ): Promise<void> {
    const pdf = new jsPDF();
    let yPosition = PDFService.MARGIN;

    // Header do relatório
    yPosition = PDFService.adicionarHeader(pdf, 'RELATÓRIO DE AVALIAÇÃO FÍSICA', yPosition);
    
    // Informações do cliente
    yPosition = PDFService.adicionarInformacoesCliente(pdf, cliente, yPosition);
    
    // Dados da avaliação
    yPosition = PDFService.adicionarDadosAvaliacaoBasica(pdf, avaliacao, yPosition);
    
    // Composição corporal
    yPosition = PDFService.adicionarComposicaoCorporal(pdf, avaliacao, yPosition);
    
    // Dobras cutâneas
    yPosition = PDFService.adicionarDobrasCutaneas(pdf, avaliacao, yPosition);
    
    // Medidas antropométricas
    yPosition = PDFService.adicionarMedidasAntropometricas(pdf, avaliacao, yPosition);
    
    // Análise e recomendações
    yPosition = PDFService.adicionarAnaliseRecomendacoes(pdf, avaliacao, yPosition);
    
    // Footer
    PDFService.adicionarFooter(pdf);
    
    // Download do arquivo
    const nomeArquivo = `Avaliacao_Fisica_${cliente.nome.replace(/\s+/g, '_')}_${avaliacao.data_avaliacao}.pdf`;
    pdf.save(nomeArquivo);
  }

  // --------------------------------------------------------------------------
  // FICHA DE TREINO PARA IMPRESSÃO
  // --------------------------------------------------------------------------
  static async gerarFichaTreino(
    treino: Treino, 
    cliente: Cliente, 
    exercicios: Exercicio[],
    series: { [exercicioId: number]: Serie[] }
  ): Promise<void> {
    const pdf = new jsPDF();
    let yPosition = PDFService.MARGIN;

    // Header
    yPosition = PDFService.adicionarHeader(pdf, 'FICHA DE TREINO', yPosition);
    
    // Informações do treino
    yPosition = PDFService.adicionarInformacoesTreino(pdf, treino, cliente, yPosition);
    
    // Exercícios por tipo de treino
    const tiposTreino = ['A', 'B', 'C', 'D', 'E', 'F'];
    
    for (const tipo of tiposTreino) {
      const exerciciosTipo = exercicios.filter(ex => ex.tipo_treino === tipo);
      if (exerciciosTipo.length > 0) {
        yPosition = PDFService.adicionarTipoTreino(pdf, tipo, exerciciosTipo, series, yPosition);
      }
    }
    
    // Footer
    PDFService.adicionarFooter(pdf);
    
    // Download
    const nomeArquivo = `Ficha_Treino_${cliente.nome.replace(/\s+/g, '_')}_${treino.nome.replace(/\s+/g, '_')}.pdf`;
    pdf.save(nomeArquivo);
  }

  // --------------------------------------------------------------------------
  // RELATÓRIO DE EVOLUÇÃO TEMPORAL
  // --------------------------------------------------------------------------
  static async gerarRelatorioEvolucao(
    cliente: Cliente,
    avaliacoes: AvaliacaoFisica[]
  ): Promise<void> {
    const pdf = new jsPDF();
    let yPosition = PDFService.MARGIN;

    // Header
    yPosition = PDFService.adicionarHeader(pdf, 'RELATÓRIO DE EVOLUÇÃO', yPosition);
    
    // Informações do cliente
    yPosition = PDFService.adicionarInformacoesCliente(pdf, cliente, yPosition);
    
    // Resumo da evolução
    yPosition = PDFService.adicionarResumoEvolucao(pdf, avaliacoes, yPosition);
    
    // Tabela comparativa
    yPosition = PDFService.adicionarTabelaComparativa(pdf, avaliacoes, yPosition);
    
    // Análise de progresso
    yPosition = PDFService.adicionarAnaliseProgresso(pdf, avaliacoes, yPosition);
    
    // Footer
    PDFService.adicionarFooter(pdf);
    
    // Download
    const nomeArquivo = `Relatorio_Evolucao_${cliente.nome.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(nomeArquivo);
  }

  // --------------------------------------------------------------------------
  // MÉTODOS AUXILIARES PARA CONSTRUÇÃO DO PDF
  // --------------------------------------------------------------------------
  
  private static adicionarHeader(pdf: jsPDF, titulo: string, yPosition: number): number {
    // Logo/Título principal
    pdf.setFontSize(24);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(255, 107, 53); // colors.energy[500]
    pdf.text('PERSONAL TRAINER', PDFService.MARGIN, yPosition);
    
    yPosition += 10;
    
    // Subtítulo
    pdf.setFontSize(18);
    pdf.setTextColor(44, 62, 80); // colors.professional[700]
    pdf.text(titulo, PDFService.MARGIN, yPosition);
    
    yPosition += 15;
    
    // Linha separadora
    pdf.setDrawColor(44, 62, 80);
    pdf.setLineWidth(0.5);
    pdf.line(PDFService.MARGIN, yPosition, PDFService.PAGE_WIDTH - PDFService.MARGIN, yPosition);
    
    return yPosition + 10;
  }

  private static adicionarInformacoesCliente(pdf: jsPDF, cliente: Cliente, yPosition: number): number {
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('DADOS DO CLIENTE', PDFService.MARGIN, yPosition);
    
    yPosition += 8;
    
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);
    
    const idade = new Date().getFullYear() - new Date(cliente.data_nascimento).getFullYear();
    
    pdf.text(`Nome: ${cliente.nome}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Idade: ${idade} anos`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Sexo: ${cliente.sexo === 'M' ? 'Masculino' : 'Feminino'}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    
    if (cliente.email) {
      pdf.text(`Email: ${cliente.email}`, PDFService.MARGIN, yPosition);
      yPosition += 6;
    }
    
    if (cliente.telefone) {
      pdf.text(`Telefone: ${cliente.telefone}`, PDFService.MARGIN, yPosition);
      yPosition += 6;
    }
    
    return yPosition + 10;
  }

  private static adicionarDadosAvaliacaoBasica(pdf: jsPDF, avaliacao: AvaliacaoFisica, yPosition: number): number {
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('DADOS DA AVALIAÇÃO', PDFService.MARGIN, yPosition);
    
    yPosition += 8;
    
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);
    
    pdf.text(`Data da Avaliação: ${formatarData(avaliacao.data_avaliacao)}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Peso: ${avaliacao.peso} kg`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Altura: ${avaliacao.altura} cm`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    
    const imc = avaliacao.peso / Math.pow(avaliacao.altura / 100, 2);
    pdf.text(`IMC: ${imc.toFixed(2)} kg/m²`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    
    return yPosition + 10;
  }

  private static adicionarComposicaoCorporal(pdf: jsPDF, avaliacao: AvaliacaoFisica, yPosition: number): number {
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('COMPOSIÇÃO CORPORAL', PDFService.MARGIN, yPosition);
    
    yPosition += 8;
    
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);
    
    const percentualGordura = avaliacao.dobras_cutaneas?.percentual_gordura || avaliacao.percentualGordura || 0;
    const massaGorda = (avaliacao.peso * percentualGordura) / 100;
    const massaMagra = avaliacao.peso - massaGorda;
    
    pdf.text(`Percentual de Gordura: ${percentualGordura.toFixed(2)}%`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Massa Gorda: ${massaGorda.toFixed(2)} kg`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Massa Magra: ${massaMagra.toFixed(2)} kg`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    
    // Classificação do percentual de gordura
    let classificacao = '';
    if (percentualGordura < 10) classificacao = 'Muito Baixo';
    else if (percentualGordura < 15) classificacao = 'Baixo';
    else if (percentualGordura < 20) classificacao = 'Normal';
    else if (percentualGordura < 25) classificacao = 'Moderado';
    else classificacao = 'Alto';
    
    pdf.text(`Classificação: ${classificacao}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    
    return yPosition + 10;
  }

  private static adicionarDobrasCutaneas(pdf: jsPDF, avaliacao: AvaliacaoFisica, yPosition: number): number {
    if (!avaliacao.dobras_cutaneas) return yPosition;
    
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('DOBRAS CUTÂNEAS (mm)', PDFService.MARGIN, yPosition);
    
    yPosition += 8;
    
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);
    
    const dobras = avaliacao.dobras_cutaneas;
    
    pdf.text(`Peitoral: ${dobras.peitoral} mm`, PDFService.MARGIN, yPosition);
    pdf.text(`Trícipital: ${dobras.tricipital} mm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;
    
    pdf.text(`Bicipital: ${dobras.bicipital} mm`, PDFService.MARGIN, yPosition);
    pdf.text(`Axilar Média: ${dobras.axilar_media} mm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;
    
    pdf.text(`Supra-ilíaca: ${dobras.suprailiaca} mm`, PDFService.MARGIN, yPosition);
    pdf.text(`Abdominal: ${dobras.abdominal} mm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;
    
    pdf.text(`Coxa: ${dobras.coxa} mm`, PDFService.MARGIN, yPosition);
    pdf.text(`Panturrilha: ${dobras.panturrilha} mm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;
    
    const somadobras = dobras.peitoral + dobras.tricipital + dobras.bicipital + 
                      dobras.axilar_media + dobras.suprailiaca + dobras.abdominal + dobras.coxa;
    
    pdf.setFont('helvetica', 'bold');
    pdf.text(`Soma das 7 dobras: ${somadobras.toFixed(2)} mm`, PDFService.MARGIN, yPosition);
    
    return yPosition + 15;
  }

  private static adicionarFooter(pdf: jsPDF): void {
    const pageCount = pdf.getNumberOfPages();
    
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      
      // Data de geração
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(128, 128, 128);
      
      const dataGeracao = new Date().toLocaleDateString('pt-BR');
      pdf.text(`Relatório gerado em ${dataGeracao}`, PDFService.MARGIN, PDFService.PAGE_HEIGHT - 10);
      
      // Número da página
      pdf.text(`Página ${i} de ${pageCount}`, PDFService.PAGE_WIDTH - PDFService.MARGIN - 30, PDFService.PAGE_HEIGHT - 10);
      
      // Linha separadora
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.3);
      pdf.line(PDFService.MARGIN, PDFService.PAGE_HEIGHT - 15, PDFService.PAGE_WIDTH - PDFService.MARGIN, PDFService.PAGE_HEIGHT - 15);
    }
  }

  private static adicionarMedidasAntropometricas(pdf: jsPDF, avaliacao: AvaliacaoFisica, yPosition: number): number {
    if (!avaliacao.medidas_antropometricas) return yPosition;

    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('MEDIDAS ANTROPOMÉTRICAS (cm)', PDFService.MARGIN, yPosition);

    yPosition += 8;

    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);

    const medidas = avaliacao.medidas_antropometricas;

    pdf.text(`Braço Direito: ${medidas.braco_direito} cm`, PDFService.MARGIN, yPosition);
    pdf.text(`Braço Esquerdo: ${medidas.braco_esquerdo} cm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;

    pdf.text(`Antebraço Direito: ${medidas.antebraco_direito} cm`, PDFService.MARGIN, yPosition);
    pdf.text(`Antebraço Esquerdo: ${medidas.antebraco_esquerdo} cm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;

    pdf.text(`Peitoral: ${medidas.peitoral} cm`, PDFService.MARGIN, yPosition);
    pdf.text(`Cintura: ${medidas.cintura} cm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;

    pdf.text(`Abdômen: ${medidas.abdomen} cm`, PDFService.MARGIN, yPosition);
    pdf.text(`Quadril: ${medidas.quadril} cm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;

    pdf.text(`Coxa Direita: ${medidas.coxa_direita} cm`, PDFService.MARGIN, yPosition);
    pdf.text(`Coxa Esquerda: ${medidas.coxa_esquerda} cm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;

    pdf.text(`Panturrilha Direita: ${medidas.panturrilha_direita} cm`, PDFService.MARGIN, yPosition);
    pdf.text(`Panturrilha Esquerda: ${medidas.panturrilha_esquerda} cm`, PDFService.MARGIN + 70, yPosition);
    yPosition += 6;

    return yPosition + 10;
  }

  private static adicionarAnaliseRecomendacoes(pdf: jsPDF, avaliacao: AvaliacaoFisica, yPosition: number): number {
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('ANÁLISE E RECOMENDAÇÕES', PDFService.MARGIN, yPosition);

    yPosition += 8;

    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);

    // Análise do nível de atividade
    pdf.text(`Nível de Atividade: ${avaliacao.nivelAtividade}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Intensidade dos Exercícios: ${avaliacao.intensidadeExercicios}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Frequência Semanal: ${avaliacao.frequenciaSemanal} vezes por semana`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Tipo de Trabalho: ${avaliacao.tipoTrabalho}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Objetivo: ${avaliacao.objetivo === 'manutenção' ? 'Manutenção' : avaliacao.objetivo === 'perda' ? 'Perda de Peso' : 'Ganho de Peso'}`, PDFService.MARGIN, yPosition);
    yPosition += 10;

    // Recomendações baseadas nos dados
    pdf.setFont('helvetica', 'bold');
    pdf.text('RECOMENDAÇÕES:', PDFService.MARGIN, yPosition);
    yPosition += 6;

    pdf.setFont('helvetica', 'normal');
    const percentualGordura = avaliacao.dobras_cutaneas?.percentual_gordura || avaliacao.percentualGordura || 0;

    if (percentualGordura > 20) {
      pdf.text('• Foco em exercícios aeróbicos para redução do percentual de gordura', PDFService.MARGIN + 5, yPosition);
      yPosition += 6;
      pdf.text('• Controle nutricional com déficit calórico moderado', PDFService.MARGIN + 5, yPosition);
      yPosition += 6;
    } else if (percentualGordura < 10) {
      pdf.text('• Foco em exercícios de força para ganho de massa muscular', PDFService.MARGIN + 5, yPosition);
      yPosition += 6;
      pdf.text('• Aumento do aporte calórico com foco em proteínas', PDFService.MARGIN + 5, yPosition);
      yPosition += 6;
    } else {
      pdf.text('• Manutenção do programa atual com progressão gradual', PDFService.MARGIN + 5, yPosition);
      yPosition += 6;
      pdf.text('• Equilíbrio entre exercícios aeróbicos e de força', PDFService.MARGIN + 5, yPosition);
      yPosition += 6;
    }

    pdf.text('• Reavaliação recomendada em 30-45 dias', PDFService.MARGIN + 5, yPosition);
    yPosition += 6;
    pdf.text('• Hidratação adequada durante os exercícios', PDFService.MARGIN + 5, yPosition);
    yPosition += 6;

    return yPosition + 10;
  }

  private static adicionarInformacoesTreino(pdf: jsPDF, treino: Treino, cliente: Cliente, yPosition: number): number {
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('INFORMAÇÕES DO TREINO', PDFService.MARGIN, yPosition);

    yPosition += 8;

    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);

    pdf.text(`Cliente: ${cliente.nome}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Nome do Treino: ${treino.nome}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Data de Início: ${formatarData(treino.data_inicio)}`, PDFService.MARGIN, yPosition);
    yPosition += 6;

    if (treino.data_fim) {
      pdf.text(`Data de Fim: ${formatarData(treino.data_fim)}`, PDFService.MARGIN, yPosition);
      yPosition += 6;
    }

    if (treino.observacoes) {
      pdf.text(`Observações: ${treino.observacoes}`, PDFService.MARGIN, yPosition);
      yPosition += 6;
    }

    return yPosition + 10;
  }

  private static adicionarTipoTreino(
    pdf: jsPDF,
    tipo: string,
    exercicios: Exercicio[],
    series: { [exercicioId: number]: Serie[] },
    yPosition: number
  ): number {
    // Verificar se precisa de nova página
    if (yPosition > PDFService.PAGE_HEIGHT - 60) {
      pdf.addPage();
      yPosition = PDFService.MARGIN;
    }

    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(255, 107, 53); // colors.energy[500]
    pdf.text(`TREINO ${tipo}`, PDFService.MARGIN, yPosition);

    yPosition += 10;

    exercicios.forEach((exercicio, index) => {
      // Verificar se precisa de nova página
      if (yPosition > PDFService.PAGE_HEIGHT - 40) {
        pdf.addPage();
        yPosition = PDFService.MARGIN;
      }

      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(44, 62, 80);
      pdf.text(`${index + 1}. ${exercicio.nome}`, PDFService.MARGIN, yPosition);

      yPosition += 8;

      // Séries do exercício
      const exercicioSeries = series[exercicio.id!] || [];
      if (exercicioSeries.length > 0) {
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(0, 0, 0);

        // Cabeçalho da tabela de séries
        pdf.text('Semana', PDFService.MARGIN + 10, yPosition);
        pdf.text('Série', PDFService.MARGIN + 35, yPosition);
        pdf.text('Repetições', PDFService.MARGIN + 55, yPosition);
        pdf.text('Carga (kg)', PDFService.MARGIN + 85, yPosition);
        pdf.text('Volume', PDFService.MARGIN + 115, yPosition);

        yPosition += 5;

        exercicioSeries.forEach(serie => {
          pdf.text(`${serie.semana}`, PDFService.MARGIN + 10, yPosition);
          pdf.text(`${serie.numero_serie}`, PDFService.MARGIN + 35, yPosition);
          pdf.text(`${serie.repeticoes}`, PDFService.MARGIN + 55, yPosition);
          pdf.text(`${serie.carga}`, PDFService.MARGIN + 85, yPosition);
          pdf.text(`${serie.volume_carga}`, PDFService.MARGIN + 115, yPosition);

          yPosition += 4;
        });
      }

      yPosition += 8;
    });

    return yPosition + 5;
  }

  private static adicionarResumoEvolucao(pdf: jsPDF, avaliacoes: AvaliacaoFisica[], yPosition: number): number {
    if (avaliacoes.length < 2) return yPosition;

    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('RESUMO DA EVOLUÇÃO', PDFService.MARGIN, yPosition);

    yPosition += 8;

    const primeira = avaliacoes[avaliacoes.length - 1];
    const ultima = avaliacoes[0];

    const diferencaPeso = ultima.peso - primeira.peso;
    const diferencaGordura = (ultima.dobras_cutaneas?.percentual_gordura || 0) - (primeira.dobras_cutaneas?.percentual_gordura || 0);

    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);

    pdf.text(`Período: ${formatarData(primeira.data_avaliacao)} a ${formatarData(ultima.data_avaliacao)}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Total de Avaliações: ${avaliacoes.length}`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Variação de Peso: ${diferencaPeso > 0 ? '+' : ''}${diferencaPeso.toFixed(2)} kg`, PDFService.MARGIN, yPosition);
    yPosition += 6;
    pdf.text(`Variação % Gordura: ${diferencaGordura > 0 ? '+' : ''}${diferencaGordura.toFixed(2)}%`, PDFService.MARGIN, yPosition);
    yPosition += 6;

    return yPosition + 10;
  }

  private static adicionarTabelaComparativa(pdf: jsPDF, avaliacoes: AvaliacaoFisica[], yPosition: number): number {
    if (avaliacoes.length === 0) return yPosition;

    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('TABELA COMPARATIVA', PDFService.MARGIN, yPosition);

    yPosition += 8;

    // Cabeçalho da tabela
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Data', PDFService.MARGIN, yPosition);
    pdf.text('Peso', PDFService.MARGIN + 30, yPosition);
    pdf.text('% Gordura', PDFService.MARGIN + 60, yPosition);
    pdf.text('IMC', PDFService.MARGIN + 90, yPosition);

    yPosition += 6;

    // Linha separadora
    pdf.setDrawColor(200, 200, 200);
    pdf.setLineWidth(0.3);
    pdf.line(PDFService.MARGIN, yPosition, PDFService.MARGIN + 120, yPosition);

    yPosition += 4;

    // Dados das avaliações (últimas 10)
    pdf.setFont('helvetica', 'normal');
    const avaliacoesLimitadas = avaliacoes.slice(0, 10);

    avaliacoesLimitadas.forEach(avaliacao => {
      const imc = avaliacao.peso / Math.pow(avaliacao.altura / 100, 2);
      const percentualGordura = avaliacao.dobras_cutaneas?.percentual_gordura || 0;

      pdf.text(formatarData(avaliacao.data_avaliacao), PDFService.MARGIN, yPosition);
      pdf.text(`${avaliacao.peso} kg`, PDFService.MARGIN + 30, yPosition);
      pdf.text(`${percentualGordura.toFixed(1)}%`, PDFService.MARGIN + 60, yPosition);
      pdf.text(`${imc.toFixed(1)}`, PDFService.MARGIN + 90, yPosition);

      yPosition += 5;
    });

    return yPosition + 10;
  }

  private static adicionarAnaliseProgresso(pdf: jsPDF, avaliacoes: AvaliacaoFisica[], yPosition: number): number {
    if (avaliacoes.length < 2) return yPosition;

    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(44, 62, 80);
    pdf.text('ANÁLISE DE PROGRESSO', PDFService.MARGIN, yPosition);

    yPosition += 8;

    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);

    const primeira = avaliacoes[avaliacoes.length - 1];
    const ultima = avaliacoes[0];

    const diferencaPeso = ultima.peso - primeira.peso;
    const diferencaGordura = (ultima.dobras_cutaneas?.percentual_gordura || 0) - (primeira.dobras_cutaneas?.percentual_gordura || 0);

    // Análise do progresso
    if (diferencaPeso < -2) {
      pdf.text('✓ Excelente perda de peso observada', PDFService.MARGIN, yPosition);
      yPosition += 6;
    } else if (diferencaPeso > 2) {
      pdf.text('• Ganho de peso significativo - verificar composição corporal', PDFService.MARGIN, yPosition);
      yPosition += 6;
    } else {
      pdf.text('• Peso estável - foco na composição corporal', PDFService.MARGIN, yPosition);
      yPosition += 6;
    }

    if (diferencaGordura < -2) {
      pdf.text('✓ Redução significativa do percentual de gordura', PDFService.MARGIN, yPosition);
      yPosition += 6;
    } else if (diferencaGordura > 2) {
      pdf.text('• Aumento do percentual de gordura - revisar estratégia', PDFService.MARGIN, yPosition);
      yPosition += 6;
    }

    // Recomendações para próximos passos
    pdf.setFont('helvetica', 'bold');
    pdf.text('PRÓXIMOS PASSOS:', PDFService.MARGIN, yPosition);
    yPosition += 6;

    pdf.setFont('helvetica', 'normal');
    pdf.text('• Manter consistência no programa de treinamento', PDFService.MARGIN + 5, yPosition);
    yPosition += 6;
    pdf.text('• Ajustar intensidade conforme evolução', PDFService.MARGIN + 5, yPosition);
    yPosition += 6;
    pdf.text('• Próxima reavaliação em 4-6 semanas', PDFService.MARGIN + 5, yPosition);

    return yPosition + 10;
  }
}
