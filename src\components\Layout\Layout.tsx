import React, { ReactNode, useState } from 'react';
import { 
  AppBar, 
  Box, 
  CssBaseline, 
  Di<PERSON>r, 
  Drawer, 
  IconButton, 
  List, 
  ListItem, 
  ListItemButton, 
  ListItemIcon, 
  ListItemText, 
  Toolbar, 
  Typography,
  useTheme,
  useMediaQuery,
  Avatar,
  Badge,
  Tooltip,
  Stack
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import PeopleIcon from '@mui/icons-material/People';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import AssessmentIcon from '@mui/icons-material/Assessment';
import HomeIcon from '@mui/icons-material/Home';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import { useNavigate, useLocation } from 'react-router-dom';

import { Logo } from '../common/Logo';
import { colors } from '../../styles/colors';

const drawerWidth = 280;

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const menuItems = [
    { text: 'Início', icon: <HomeIcon />, path: '/' },
    { text: 'Clientes', icon: <PeopleIcon />, path: '/clientes' },
    { text: 'Avaliações Físicas', icon: <AssessmentIcon />, path: '/avaliacoes' },
    { text: 'Treinos', icon: <FitnessCenterIcon />, path: '/treinos' },
  ];

  const drawer = (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      bgcolor: colors.ocean,
      color: colors.cloud
    }}>
      <Box sx={{ 
        p: 3, 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center',
        background: colors.ocean
      }}>
        <Box 
          sx={{ 
            width: 70,
            height: 70,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50%',
            mb: 2,
            background: colors.sea,
            boxShadow: `0px 4px 10px ${colors.sea}4D`
          }}
        >
          <img
            src="/logo-icon.png"
            alt="Logo Icon"
            style={{ width: 48, height: 48 }}
          />
        </Box>
        <Typography 
          variant="h6" 
          sx={{ 
            fontWeight: 'bold',
            color: colors.cloud
          }}
        >
          Hyper Personal
        </Typography>
      </Box>
      
      <Divider sx={{ mb: 2, borderColor: `${colors.cloud}1A` }} />
      
      <List sx={{ px: 2, flexGrow: 1 }}>
        {menuItems.map((item) => {
          const isSelected = location.pathname === item.path;
          return (
            <ListItem key={item.text} disablePadding sx={{ mb: 1 }}>
              <ListItemButton 
                selected={isSelected}
                onClick={() => {
                  navigate(item.path);
                  if (isMobile) {
                    setMobileOpen(false);
                  }
                }}
                sx={{
                  borderRadius: 3,
                  '&.Mui-selected': {
                    bgcolor: colors.sea,
                    '&:hover': {
                      bgcolor: colors.ocean
                    }
                  },
                  '&:hover': {
                    bgcolor: `${colors.cloud}1A`
                  }
                }}
              >
                <ListItemIcon sx={{ 
                  color: isSelected ? colors.cloud : `${colors.cloud}B3`,
                  minWidth: 40
                }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text} 
                  sx={{ 
                    '& .MuiTypography-root': {
                      fontWeight: isSelected ? 600 : 400,
                      color: isSelected ? colors.cloud : `${colors.cloud}B3`
                    }
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
      
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="caption" sx={{ color: `${colors.cloud}80` }}>
          Versão 1.0.0
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', flexDirection: 'column' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          bgcolor: colors.cloud,
          color: colors.ocean,
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.05)'
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              color="inherit"
              aria-label="abrir menu"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ 
                mr: 2, 
                display: { md: 'none' },
                color: colors.sea
              }}
            >
              <MenuIcon />
            </IconButton>
            <Typography 
              variant="h6" 
              noWrap 
              component="div"
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                fontWeight: 'bold',
                color: colors.ocean
              }}
            >
              Sistema de Gerenciamento
            </Typography>
          </Box>

          <Stack direction="row" spacing={2} alignItems="center">
            <Tooltip title="Notificações" arrow>
              <IconButton 
                size="large" 
                sx={{ 
                  bgcolor: `${colors.sea}0D`, 
                  borderRadius: '50%',
                  '&:hover': {
                    bgcolor: `${colors.sea}1A`
                  }
                }}
              >
                <Badge badgeContent={3} color="primary">
                  <NotificationsIcon sx={{ color: colors.sea }} />
                </Badge>
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Perfil" arrow>
              <IconButton 
                sx={{ 
                  ml: 1,
                  bgcolor: `${colors.sea}0D`,
                  '&:hover': {
                    bgcolor: `${colors.sea}1A`
                  },
                  borderRadius: '50%'
                }}
              >
                <Avatar 
                  sx={{ 
                    bgcolor: colors.sea,
                    color: colors.cloud,
                    boxShadow: `0px 2px 4px ${colors.sea}33`
                  }}
                >
                  <AccountCircleIcon />
                </Avatar>
              </IconButton>
            </Tooltip>
          </Stack>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
        aria-label="menu de navegação"
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              boxShadow: '4px 0px 10px rgba(0, 0, 0, 0.1)'
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              boxShadow: '4px 0px 10px rgba(0, 0, 0, 0.05)'
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 2, sm: 3, md: 4 },
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          mt: { xs: '56px', sm: '64px' },
          display: 'flex',
          flexDirection: 'column',
          minHeight: { xs: 'calc(100vh - 56px)', sm: 'calc(100vh - 64px)' },
          bgcolor: '#F8F9FA'
        }}
      >
        <Box sx={{ flexGrow: 1 }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout; 