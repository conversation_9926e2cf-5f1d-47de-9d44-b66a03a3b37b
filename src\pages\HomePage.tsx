import React, { useEffect, useState } from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent,
  CardActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  Container,
  useTheme,
  Tooltip,
  IconButton,
  Stack
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import PeopleIcon from '@mui/icons-material/People';
import AssessmentIcon from '@mui/icons-material/Assessment';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import BarChartIcon from '@mui/icons-material/BarChart';

import InfoIcon from '@mui/icons-material/Info';
import { useAppContext } from '../contexts/AppContext';
import { useAvaliacaoFisica } from '../contexts/AvaliacaoFisicaContext';
import { useTreino } from '../contexts/TreinoContext';
import { Cliente } from '../models/Cliente';
import { AvaliacaoFisica } from '../models/AvaliacaoFisica';
import { Treino } from '../models/Treino';
import { EmptyState } from '../components/common/EmptyState';
import {
  AnimatedBox,
  AnimatedCard,
  AnimatedCounter,
  FadeInWhenVisible,
  AnimatedList
} from '../components/common/AnimatedComponents';
import { useAppToast } from '../contexts/ToastContext';
import { ExportPDFButton } from '../components/common/ExportPDFButton';
import { colors } from '../styles/colors';

interface Estatisticas {
  totalClientes: number;
  totalAvaliacoes: number;
  totalTreinos: number;
  clientesRecentes: Cliente[];
  avaliacoesRecentes: AvaliacaoFisica[];
  treinosRecentes: Treino[];
}

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { clientes, buscarClientes } = useAppContext();
  const { avaliacoes, carregarAvaliacoes } = useAvaliacaoFisica();
  const { treinos } = useTreino();
  const toast = useAppToast();
  
  const [estatisticas, setEstatisticas] = useState<Estatisticas>({
    totalClientes: 0,
    totalAvaliacoes: 0,
    totalTreinos: 0,
    clientesRecentes: [],
    avaliacoesRecentes: [],
    treinosRecentes: []
  });

  useEffect(() => {
    const carregarDados = async () => {
      await buscarClientes();
      await carregarAvaliacoes();
    };
    
    carregarDados();
  }, [buscarClientes, carregarAvaliacoes]);

  useEffect(() => {
    setEstatisticas({
      totalClientes: clientes.length,
      totalAvaliacoes: avaliacoes.length,
      totalTreinos: treinos.length,
      clientesRecentes: clientes.slice(0, 5),
      avaliacoesRecentes: avaliacoes.slice(0, 5),
      treinosRecentes: treinos.slice(0, 5)
    });
  }, [clientes, avaliacoes, treinos]);

  const cards = [
    {
      title: 'Clientes',
      icon: <PeopleIcon fontSize="large" />,
      count: estatisticas.totalClientes,
      action: () => navigate('/clientes'),
      buttonText: 'Gerenciar Clientes',
      color: colors.sea,
      description: 'Total de clientes ativos cadastrados no sistema'
    },
    {
      title: 'Avaliações Físicas',
      icon: <AssessmentIcon fontSize="large" />,
      count: estatisticas.totalAvaliacoes,
      action: () => navigate('/avaliacoes'),
      buttonText: 'Gerenciar Avaliações',
      color: colors.sea,
      description: 'Avaliações físicas realizadas'
    },
    {
      title: 'Treinos',
      icon: <FitnessCenterIcon fontSize="large" />,
      count: estatisticas.totalTreinos,
      action: () => navigate('/treinos'),
      buttonText: 'Gerenciar Treinos',
      color: colors.sea,
      description: 'Planilhas de treino ativas'
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 3, md: 4 } }}>
      <AnimatedBox animation="fadeInUp" duration={0.8}>
        <Box sx={{
          mb: { xs: 3, sm: 4, md: 6 },
          textAlign: 'center',
          background: `${colors.sea}0A`,
          borderRadius: 4,
          p: { xs: 2, sm: 3, md: 4 }
        }}>
          <AnimatedBox animation="slideInDown" delay={0.3}>
            <Typography
              variant="h3"
              gutterBottom
              fontWeight="bold"
              sx={{
                color: colors.ocean,
                fontSize: { xs: '1.75rem', sm: '2.25rem', md: '3rem' }
              }}
            >
              Bem-vindo ao Hyper
            </Typography>
          </AnimatedBox>
          <AnimatedBox animation="fadeInUp" delay={0.6}>
            <Typography
              variant="h6"
              sx={{
                color: `${colors.ocean}B3`,
                maxWidth: 800,
                mx: 'auto',
                fontSize: { xs: '0.875rem', sm: '1rem', md: '1.25rem' }
              }}
            >
              Sistema completo de gerenciamento para Personal Trainers.
              Gerencie seus clientes, avaliações físicas e treinos em um só lugar.
            </Typography>
          </AnimatedBox>
        </Box>
      </AnimatedBox>

      <AnimatedList staggerDelay={0.2}>
        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: { xs: 3, sm: 4, md: 6 } }}>
          {cards.map((card, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <AnimatedCard
                hoverElevation={true}
                glowColor={card.color}
              >
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderRadius: { xs: 3, md: 4 },
                    overflow: 'hidden',
                    boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
                  }}
                >
              <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: { xs: 2, md: 3 } }}>
                <Box 
                  sx={{ 
                    display: 'inline-flex',
                    p: { xs: 1.5, md: 2 },
                    borderRadius: '50%',
                    mb: 2,
                    bgcolor: `${colors.sea}1A`
                  }}
                >
                  <Box sx={{ color: card.color }}>
                    {card.icon}
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                  <Typography 
                    variant="h5" 
                    component="div" 
                    sx={{ 
                      fontWeight: 500, 
                      color: colors.ocean,
                      fontSize: { xs: '1.25rem', md: '1.5rem' }
                    }}
                  >
                    {card.title}
                  </Typography>
                  <Tooltip title={card.description} arrow>
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <InfoIcon fontSize="small" sx={{ color: `${colors.ocean}80` }} />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Typography
                  variant="h3"
                  sx={{
                    mb: 2,
                    color: card.color,
                    fontWeight: 'bold',
                    fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' }
                  }}
                >
                  <AnimatedCounter
                    value={card.count}
                    duration={2000 + (index * 500)}
                  />
                </Typography>
                <Typography variant="body2" sx={{ color: `${colors.ocean}99`, mb: 2 }}>
                  {card.description}
                </Typography>
              </CardContent>
              <CardActions sx={{ p: { xs: 2, md: 3 }, pt: 0 }}>
                <Button 
                  variant="contained" 
                  fullWidth 
                  onClick={card.action}
                  startIcon={card.icon}
                  sx={{ 
                    bgcolor: card.color,
                    borderRadius: 3,
                    py: { xs: 1, md: 1.2 },
                    '&:hover': {
                      bgcolor: colors.ocean,
                      boxShadow: `0px 4px 8px ${colors.sea}33`
                    }
                  }}
                >
                  {card.buttonText}
                </Button>
              </CardActions>
            </Card>
          </AnimatedCard>
        </Grid>
      ))}
    </Grid>
  </AnimatedList>

      <Grid container spacing={{ xs: 2, sm: 3 }}>
        <Grid item xs={12} md={6}>
          <FadeInWhenVisible delay={0.3}>
            <AnimatedCard hoverElevation={true} glowColor={colors.professional[500]}>
              <Paper
                sx={{
                  p: { xs: 2, md: 3 },
                  height: '100%',
                  borderRadius: { xs: 3, md: 4 },
                  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
                  background: colors.cloud
                }}
              >
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
              <Box sx={{ 
                bgcolor: `${colors.sea}1A`, 
                p: 1, 
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <PersonAddIcon sx={{ color: colors.sea }} />
              </Box>
              <Typography 
                variant="h6" 
                sx={{ 
                  fontWeight: 500, 
                  color: colors.ocean,
                  fontSize: { xs: '1rem', md: '1.25rem' }
                }}
              >
                Clientes Recentes
              </Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Tooltip title="Últimos 5 clientes cadastrados" arrow>
                <IconButton size="small" sx={{ bgcolor: `${colors.ocean}0A`, borderRadius: '50%' }}>
                  <InfoIcon fontSize="small" sx={{ color: `${colors.ocean}80` }} />
                </IconButton>
              </Tooltip>
            </Stack>
            <Divider sx={{ mb: 2, borderColor: `${colors.ocean}1A` }} />
            <List sx={{ 
              maxHeight: { xs: '300px', md: '400px' },
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: `${colors.sea}4D`,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: colors.sea,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: colors.ocean,
              }
            }}>
              {estatisticas.clientesRecentes.length > 0 ? (
                estatisticas.clientesRecentes.map((cliente: any) => (
                  <ListItem 
                    key={cliente.id}
                    sx={{
                      borderRadius: 3,
                      mb: 1,
                      '&:hover': {
                        bgcolor: `${colors.sea}0A`
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Box sx={{ 
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        bgcolor: `${colors.sea}1A`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: colors.sea
                      }}>
                        <PeopleIcon />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                          {cliente.nome}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                          {cliente.email}
                        </Typography>
                      }
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => navigate(`/clientes/${cliente.id}`)}
                      sx={{
                        borderColor: colors.sea,
                        color: colors.sea,
                        '&:hover': {
                          borderColor: colors.ocean,
                          color: colors.ocean,
                          bgcolor: `${colors.sea}0A`
                        }
                      }}
                    >
                      Ver Detalhes
                    </Button>
                  </ListItem>
                ))
              ) : (
                <Box sx={{ py: 2 }}>
                  <EmptyState
                    variant="clientes"
                    title="Nenhum cliente cadastrado"
                    description="Adicione seu primeiro cliente para começar a usar o sistema."
                    actionLabel="Adicionar Cliente"
                    onAction={() => navigate('/clientes')}
                    size="small"
                  />
                </Box>
              )}
            </List>
          </Paper>
        </AnimatedCard>
      </FadeInWhenVisible>
    </Grid>

        <Grid item xs={12} md={6}>
          <FadeInWhenVisible delay={0.5}>
            <AnimatedCard hoverElevation={true} glowColor={colors.health[500]}>
              <Paper
                sx={{
                  p: { xs: 2, md: 3 },
                  height: '100%',
                  borderRadius: { xs: 3, md: 4 },
                  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
                  background: colors.cloud
                }}
              >
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
              <Box sx={{ 
                bgcolor: `${colors.sea}1A`, 
                p: 1, 
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <AssessmentIcon sx={{ color: colors.sea }} />
              </Box>
              <Typography 
                variant="h6" 
                sx={{ 
                  fontWeight: 500, 
                  color: colors.ocean,
                  fontSize: { xs: '1rem', md: '1.25rem' }
                }}
              >
                Últimas Avaliações
              </Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Tooltip title="Últimas 5 avaliações realizadas" arrow>
                <IconButton size="small" sx={{ bgcolor: `${colors.ocean}0A`, borderRadius: '50%' }}>
                  <InfoIcon fontSize="small" sx={{ color: `${colors.ocean}80` }} />
                </IconButton>
              </Tooltip>
            </Stack>
            <Divider sx={{ mb: 2, borderColor: `${colors.ocean}1A` }} />
            <List sx={{ 
              maxHeight: { xs: '300px', md: '400px' },
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: `${colors.sea}4D`,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: colors.sea,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: colors.ocean,
              }
            }}>
              {estatisticas.avaliacoesRecentes.length > 0 ? (
                estatisticas.avaliacoesRecentes.map((avaliacao: any) => (
                  <ListItem 
                    key={avaliacao.id}
                    sx={{
                      borderRadius: 3,
                      mb: 1,
                      '&:hover': {
                        bgcolor: `${colors.sea}0A`
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Box sx={{ 
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        bgcolor: `${colors.sea}1A`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: colors.sea
                      }}>
                        <BarChartIcon />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" sx={{ color: colors.ocean, fontWeight: 500 }}>
                          {avaliacao.cliente?.nome || 'Cliente não encontrado'}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                          {new Date(avaliacao.data).toLocaleDateString()}
                        </Typography>
                      }
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => navigate(`/avaliacoes/${avaliacao.id}`)}
                      sx={{
                        borderColor: colors.sea,
                        color: colors.sea,
                        '&:hover': {
                          borderColor: colors.ocean,
                          color: colors.ocean,
                          bgcolor: `${colors.sea}0A`
                        }
                      }}
                    >
                      Ver Detalhes
                    </Button>
                  </ListItem>
                ))
              ) : (
                <Box sx={{ py: 2 }}>
                  <EmptyState
                    variant="avaliacoes"
                    title="Nenhuma avaliação realizada"
                    description="Realize a primeira avaliação física para acompanhar a evolução dos clientes."
                    actionLabel="Nova Avaliação"
                    onAction={() => navigate('/avaliacoes')}
                    size="small"
                  />
                </Box>
              )}
            </List>
          </Paper>
        </AnimatedCard>
      </FadeInWhenVisible>
    </Grid>
  </Grid>

  {/* Demonstração de Toasts - Remover em produção */}
  <Box sx={{ mt: 4, p: 3, bgcolor: `${colors.professional[50]}`, borderRadius: 3 }}>
    <Typography variant="h6" sx={{ mb: 2, color: colors.ocean }}>
      🔔 Demonstração do Sistema de Notificações
    </Typography>
    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
      <Button
        variant="contained"
        onClick={() => toast.showSuccess('Operação realizada com sucesso!')}
        sx={{ bgcolor: colors.health[500] }}
      >
        Sucesso
      </Button>
      <Button
        variant="contained"
        onClick={() => toast.showError('Erro ao processar solicitação!')}
        sx={{ bgcolor: '#f44336' }}
      >
        Erro
      </Button>
      <Button
        variant="contained"
        onClick={() => toast.showWarning('Atenção: Verifique os dados!')}
        sx={{ bgcolor: colors.energy[500] }}
      >
        Aviso
      </Button>
      <Button
        variant="contained"
        onClick={() => toast.showInfo('Informação importante!')}
        sx={{ bgcolor: colors.professional[500] }}
      >
        Info
      </Button>
      <Button
        variant="outlined"
        onClick={() => toast.clienteAdicionado('João Silva')}
        sx={{ borderColor: colors.sea, color: colors.sea }}
      >
        Cliente Adicionado
      </Button>
      <Button
        variant="outlined"
        onClick={() => toast.exportacaoConcluida()}
        sx={{ borderColor: colors.sea, color: colors.sea }}
      >
        Exportação
      </Button>
    </Box>
  </Box>

  {/* Demonstração do Sistema de Impressão */}
  <Box sx={{ mt: 4, p: 3, bgcolor: `${colors.energy[50]}`, borderRadius: 3 }}>
    <Typography variant="h6" sx={{ mb: 2, color: colors.ocean }}>
      📄 Sistema de Impressão HTML (Novo!)
    </Typography>
    <Typography variant="body2" sx={{ mb: 3, color: colors.professional[600] }}>
      Sistema de exportação renovado - sem dependências problemáticas, usando HTML/CSS para impressão profissional.
    </Typography>
    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
      {clientes.length > 0 && (
        <ExportPDFButton
          variant="menu"
          cliente={clientes[0]}
          avaliacoes={avaliacoes.filter(av => av.cliente_id === clientes[0].id)}
          size="medium"
        />
      )}
      <Button
        variant="outlined"
        onClick={() => toast.showInfo('Sistema de impressão HTML implementado com sucesso!')}
        sx={{ borderColor: colors.energy[500], color: colors.energy[500] }}
      >
        ✅ Conflitos PDF Resolvidos
      </Button>
    </Box>
  </Box>
</Container>
);
};

export default HomePage; 