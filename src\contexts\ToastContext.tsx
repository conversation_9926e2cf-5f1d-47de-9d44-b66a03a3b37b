import React, { createContext, useContext, useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>bar, Alert, AlertColor, Slide, SlideProps } from '@mui/material';
import { colors } from '../styles/colors';

// ============================================================================
// TOAST CONTEXT - SISTEMA DE NOTIFICAÇÕES
// ============================================================================

interface ToastMessage {
  id: string;
  message: string;
  type: AlertColor;
  duration?: number;
  action?: React.ReactNode;
}

interface ToastContextType {
  showToast: (message: string, type?: AlertColor, duration?: number, action?: React.ReactNode) => void;
  showSuccess: (message: string, duration?: number) => void;
  showError: (message: string, duration?: number) => void;
  showWarning: (message: string, duration?: number) => void;
  showInfo: (message: string, duration?: number) => void;
  hideToast: (id?: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Componente de transição personalizada
function SlideTransition(props: SlideProps) {
  return <Slide {...props} direction="up" />;
}

// ============================================================================
// TOAST PROVIDER
// ============================================================================

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const showToast = useCallback((
    message: string, 
    type: AlertColor = 'info', 
    duration: number = 4000,
    action?: React.ReactNode
  ) => {
    const id = generateId();
    const newToast: ToastMessage = {
      id,
      message,
      type,
      duration,
      action
    };

    setToasts(prev => [...prev, newToast]);

    // Auto-remove após a duração especificada
    if (duration > 0) {
      setTimeout(() => {
        hideToast(id);
      }, duration);
    }
  }, []);

  const showSuccess = useCallback((message: string, duration: number = 4000) => {
    showToast(message, 'success', duration);
  }, [showToast]);

  const showError = useCallback((message: string, duration: number = 6000) => {
    showToast(message, 'error', duration);
  }, [showToast]);

  const showWarning = useCallback((message: string, duration: number = 5000) => {
    showToast(message, 'warning', duration);
  }, [showToast]);

  const showInfo = useCallback((message: string, duration: number = 4000) => {
    showToast(message, 'info', duration);
  }, [showToast]);

  const hideToast = useCallback((id?: string) => {
    if (id) {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    } else {
      // Remove o primeiro toast se nenhum ID for especificado
      setToasts(prev => prev.slice(1));
    }
  }, []);



  const handleClose = (id: string) => {
    hideToast(id);
  };

  return (
    <ToastContext.Provider value={{
      showToast,
      showSuccess,
      showError,
      showWarning,
      showInfo,
      hideToast
    }}>
      {children}
      
      {/* Renderizar todos os toasts */}
      {toasts.map((toast, index) => (
        <Snackbar
          key={toast.id}
          open={true}
          autoHideDuration={toast.duration}
          onClose={() => handleClose(toast.id)}
          TransitionComponent={SlideTransition}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          sx={{
            // Empilhar toasts verticalmente
            bottom: `${16 + (index * 70)}px !important`,
            zIndex: 9999 + index,
          }}
        >
          <Alert
            onClose={() => handleClose(toast.id)}
            severity={toast.type}
            variant="filled"
            action={toast.action}
            sx={{
              minWidth: 300,
              maxWidth: 500,
              borderRadius: 3,
              fontWeight: 500,
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
              // Cores customizadas baseadas no design system
              ...(toast.type === 'success' && {
                backgroundColor: colors.health[500],
                color: colors.white,
                '& .MuiAlert-icon': {
                  color: colors.white
                },
                '& .MuiAlert-action': {
                  color: colors.white
                }
              }),
              ...(toast.type === 'error' && {
                backgroundColor: '#f44336',
                color: colors.white,
                '& .MuiAlert-icon': {
                  color: colors.white
                },
                '& .MuiAlert-action': {
                  color: colors.white
                }
              }),
              ...(toast.type === 'warning' && {
                backgroundColor: colors.energy[500],
                color: colors.white,
                '& .MuiAlert-icon': {
                  color: colors.white
                },
                '& .MuiAlert-action': {
                  color: colors.white
                }
              }),
              ...(toast.type === 'info' && {
                backgroundColor: colors.professional[500],
                color: colors.white,
                '& .MuiAlert-icon': {
                  color: colors.white
                },
                '& .MuiAlert-action': {
                  color: colors.white
                }
              }),
              // Animação de entrada
              animation: 'slideInUp 0.3s ease-out',
              '@keyframes slideInUp': {
                from: {
                  transform: 'translateY(100%)',
                  opacity: 0
                },
                to: {
                  transform: 'translateY(0)',
                  opacity: 1
                }
              }
            }}
          >
            {toast.message}
          </Alert>
        </Snackbar>
      ))}
    </ToastContext.Provider>
  );
};

// ============================================================================
// HOOK PARA USAR TOAST
// ============================================================================

export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// ============================================================================
// HOOK COM MENSAGENS PRÉ-DEFINIDAS PARA O SISTEMA
// ============================================================================

export const useAppToast = () => {
  const toast = useToast();

  return {
    ...toast,
    
    // Mensagens para Clientes
    clienteAdicionado: (nome: string) => 
      toast.showSuccess(`Cliente ${nome} adicionado com sucesso!`),
    clienteAtualizado: (nome: string) => 
      toast.showSuccess(`Cliente ${nome} atualizado com sucesso!`),
    clienteRemovido: (nome: string) => 
      toast.showSuccess(`Cliente ${nome} removido com sucesso!`),
    
    // Mensagens para Avaliações
    avaliacaoAdicionada: () => 
      toast.showSuccess('Avaliação física adicionada com sucesso!'),
    avaliacaoAtualizada: () => 
      toast.showSuccess('Avaliação física atualizada com sucesso!'),
    avaliacaoRemovida: () => 
      toast.showSuccess('Avaliação física removida com sucesso!'),
    
    // Mensagens para Treinos
    treinoAdicionado: (nome: string) => 
      toast.showSuccess(`Treino "${nome}" criado com sucesso!`),
    treinoAtualizado: (nome: string) => 
      toast.showSuccess(`Treino "${nome}" atualizado com sucesso!`),
    treinoRemovido: (nome: string) => 
      toast.showSuccess(`Treino "${nome}" removido com sucesso!`),
    
    // Mensagens para Exercícios
    exercicioAdicionado: (nome: string) => 
      toast.showSuccess(`Exercício "${nome}" adicionado!`),
    exercicioRemovido: (nome: string) => 
      toast.showSuccess(`Exercício "${nome}" removido!`),
    
    // Mensagens de Erro
    erroCarregarDados: () => 
      toast.showError('Erro ao carregar dados. Tente novamente.'),
    erroSalvarDados: () => 
      toast.showError('Erro ao salvar dados. Verifique os campos e tente novamente.'),
    erroConexao: () => 
      toast.showError('Erro de conexão. Verifique sua internet.'),
    
    // Mensagens de Validação
    camposObrigatorios: () => 
      toast.showWarning('Preencha todos os campos obrigatórios.'),
    dadosInvalidos: () => 
      toast.showWarning('Verifique os dados inseridos.'),
    
    // Mensagens de Info
    dadosSalvos: () => 
      toast.showInfo('Dados salvos automaticamente.'),
    exportacaoIniciada: () => 
      toast.showInfo('Iniciando exportação...'),
    exportacaoConcluida: () => 
      toast.showSuccess('Exportação concluída com sucesso!'),
    
    // Mensagens de Ação
    operacaoCancelada: () => 
      toast.showInfo('Operação cancelada.'),
    alteracoesPerdidas: () => 
      toast.showWarning('Alterações não salvas serão perdidas.'),
  };
};

export default ToastContext;
