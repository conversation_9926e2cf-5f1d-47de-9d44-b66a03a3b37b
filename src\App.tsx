import React, { useEffect } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { ptBR } from '@mui/material/locale';
import { AppProvider } from './contexts/AppContext';
import { AvaliacaoFisicaProvider } from './contexts/AvaliacaoFisicaContext';
import TreinoProvider from './contexts/TreinoContext';
import { ClienteProvider } from './contexts/ClienteContext';
import { ToastProvider } from './contexts/ToastContext';
import Layout from './components/Layout/Layout';
import HomePage from './pages/HomePage';
import ClientesPage from './pages/ClientesPage';
import AvaliacoesPage from './pages/AvaliacoesPage';
import TreinosPage from './pages/TreinosPage';
import NotFoundPage from './pages/NotFoundPage';
import { initializeDatabase } from './db/init';
import { PageTransition } from './components/common/AnimatedComponents';
import theme from './styles/theme';

function App() {
  useEffect(() => {
    initializeDatabase();
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <ToastProvider>
        <AppProvider>
          <ClienteProvider>
            <AvaliacaoFisicaProvider>
              <TreinoProvider>
                <BrowserRouter>
                  <Layout>
                    <Routes>
                      <Route path="/" element={<HomePage />} />
                      <Route path="/clientes" element={<ClientesPage />} />
                      <Route path="/avaliacoes" element={<AvaliacoesPage />} />
                      <Route path="/treinos" element={<TreinosPage />} />
                      <Route path="*" element={<NotFoundPage />} />
                    </Routes>
                  </Layout>
                </BrowserRouter>
              </TreinoProvider>
            </AvaliacaoFisicaProvider>
          </ClienteProvider>
        </AppProvider>
      </ToastProvider>
    </ThemeProvider>
  );
}

export default App;
