import React, { useState } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Typography,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Avatar,
  Chip
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import { Cliente } from '../../models/Cliente';
import { useAppContext } from '../../contexts/AppContext';
import { TableSkeleton } from '../common/LoadingSkeletons';
import { EmptyClientes, EmptySearch } from '../common/EmptyState';
import { colors } from '../../styles/colors';

interface ClienteListProps {
  onEditar: (cliente: Cliente) => void;
  onVisualizar: (cliente: Cliente) => void;
}

const ClienteList: React.FC<ClienteListProps> = ({ onEditar, onVisualizar }) => {
  const { clientes, excluirCliente, buscarClientesPorTermo, carregando } = useAppContext();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [termoBusca, setTermoBusca] = useState('');
  const [clienteParaExcluir, setClienteParaExcluir] = useState<Cliente | null>(null);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleBuscar = () => {
    buscarClientesPorTermo(termoBusca);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleBuscar();
    }
  };

  const handleExcluirClick = (cliente: Cliente) => {
    setClienteParaExcluir(cliente);
  };

  const handleConfirmarExclusao = async () => {
    if (clienteParaExcluir?.id) {
      await excluirCliente(clienteParaExcluir.id);
      setClienteParaExcluir(null);
    }
  };

  const handleCancelarExclusao = () => {
    setClienteParaExcluir(null);
  };

  // Calcular idade a partir da data de nascimento
  const calcularIdade = (dataNascimento: string): number => {
    const hoje = new Date();
    const nascimento = new Date(dataNascimento);
    let idade = hoje.getFullYear() - nascimento.getFullYear();
    const m = hoje.getMonth() - nascimento.getMonth();
    
    if (m < 0 || (m === 0 && hoje.getDate() < nascimento.getDate())) {
      idade--;
    }
    
    return idade;
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(part => part[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <TextField
          label="Buscar clientes"
          variant="outlined"
          size="small"
          fullWidth
          value={termoBusca}
          onChange={(e) => setTermoBusca(e.target.value)}
          onKeyPress={handleKeyPress}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: colors.sea }} />
              </InputAdornment>
            ),
            sx: {
              borderRadius: 3,
              '& fieldset': {
                borderColor: colors.sea,
              },
              '&:hover fieldset': {
                borderColor: colors.sea,
              },
              '&.Mui-focused fieldset': {
                borderColor: colors.sea,
              }
            }
          }}
          sx={{ maxWidth: 500 }}
        />
        <Button 
          variant="contained" 
          onClick={handleBuscar}
          sx={{ 
            bgcolor: colors.sea,
            borderRadius: 3,
            py: 1,
            px: 3,
            minWidth: 100,
            '&:hover': {
              bgcolor: colors.ocean,
              boxShadow: `0px 4px 8px ${colors.sea}33`
            }
          }}
        >
          Buscar
        </Button>
      </Box>

      {carregando ? (
        <TableSkeleton rows={rowsPerPage} animated={true} />
      ) : (
        <Paper
          elevation={0}
          sx={{
            borderRadius: 4,
            border: `1px solid ${colors.sea}`,
            overflow: 'hidden',
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
            bgcolor: colors.cloud
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: `${colors.ocean}0A` }}>
                  <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Nome</TableCell>
                  <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Idade</TableCell>
                  <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Sexo</TableCell>
                  <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Email</TableCell>
                  <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Telefone</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Ações</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {clientes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} sx={{ p: 0, border: 'none' }}>
                    {termoBusca ? (
                      <EmptySearch
                        searchTerm={termoBusca}
                        onClearFilters={() => setTermoBusca('')}
                        size="small"
                      />
                    ) : (
                      <EmptyClientes
                        size="small"
                      />
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                clientes
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((cliente) => (
                    <TableRow 
                      key={cliente.id} 
                      hover
                      sx={{ 
                        '&:hover': { 
                          bgcolor: `${colors.sea}1A` 
                        }
                      }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar 
                            sx={{ 
                              bgcolor: `${colors.sea}1A`, 
                              color: colors.sea,
                              width: 36,
                              height: 36,
                              mr: 2,
                              fontSize: '0.9rem'
                            }}
                          >
                            {getInitials(cliente.nome)}
                          </Avatar>
                          <Typography sx={{ fontWeight: 500, color: colors.ocean }}>
                            {cliente.nome}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={`${calcularIdade(cliente.data_nascimento)} anos`} 
                          size="small"
                          sx={{ 
                            bgcolor: `${colors.sea}0A`,
                            color: colors.sea,
                            fontWeight: 500,
                            borderRadius: '12px'
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ color: `${colors.ocean}99` }}>
                        {cliente.sexo === 'M' ? 'Masculino' : 'Feminino'}
                      </TableCell>
                      <TableCell sx={{ color: `${colors.ocean}99` }}>
                        {cliente.email}
                      </TableCell>
                      <TableCell sx={{ color: `${colors.ocean}99` }}>
                        {cliente.telefone}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="Visualizar">
                          <IconButton 
                            onClick={() => onVisualizar(cliente)}
                            size="small"
                            sx={{ 
                              color: colors.sea,
                              '&:hover': { 
                                bgcolor: `${colors.sea}1A` 
                              }
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Editar">
                          <IconButton 
                            onClick={() => onEditar(cliente)}
                            size="small"
                            sx={{ 
                              color: colors.sea,
                              '&:hover': { 
                                bgcolor: `${colors.sea}1A` 
                              }
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Excluir">
                          <IconButton 
                            onClick={() => handleExcluirClick(cliente)}
                            size="small"
                            sx={{ 
                              color: '#FF4D4D',
                              '&:hover': { 
                                bgcolor: 'rgba(255, 77, 77, 0.1)' 
                              }
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={clientes.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage="Itens por página:"
            labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
            sx={{
              borderTop: `1px solid ${colors.ocean}1A`,
              '.MuiTablePagination-select': {
                color: colors.ocean
              },
              '.MuiTablePagination-displayedRows': {
                color: colors.ocean
              },
              '.MuiTablePagination-selectLabel': {
                color: colors.ocean
              }
            }}
          />
        </Paper>
      )}

      {/* Diálogo de confirmação de exclusão */}
      <Dialog
        open={!!clienteParaExcluir}
        onClose={handleCancelarExclusao}
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <DialogTitle sx={{ color: colors.ocean, fontWeight: 600 }}>
          Confirmar Exclusão
        </DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ color: `${colors.ocean}99` }}>
            Tem certeza que deseja excluir o cliente {clienteParaExcluir?.nome}? Esta ação não pode ser desfeita.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button 
            onClick={handleCancelarExclusao}
            sx={{ 
              color: `${colors.ocean}99`,
              '&:hover': {
                bgcolor: `${colors.ocean}0A`,
                color: colors.ocean
              }
            }}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleConfirmarExclusao} 
            variant="contained"
            sx={{ 
              bgcolor: '#FF4D4D',
              '&:hover': {
                bgcolor: '#E60000'
              }
            }}
          >
            Excluir
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ClienteList; 