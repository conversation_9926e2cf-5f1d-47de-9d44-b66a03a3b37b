import React, { useState } from 'react';
import {
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Tooltip,
  Box
} from '@mui/material';
import {
  PictureAsPdf as PdfIcon,
  Assessment as AssessmentIcon,
  FitnessCenter as FitnessCenterIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { AvaliacaoFisica } from '../../models/AvaliacaoFisica';
import { Cliente } from '../../models/Cliente';
import { Treino, Exercicio, Serie } from '../../models/Treino';
import { PrintService } from '../../services/PrintService';
import { colors } from '../../styles/colors';

// ============================================================================
// EXPORT PDF BUTTON - BOTÕES DE EXPORTAÇÃO PROFISSIONAIS
// ============================================================================

interface ExportPDFButtonProps {
  variant?: 'avaliacao' | 'treino' | 'evolucao' | 'menu';
  avaliacao?: AvaliacaoFisica;
  cliente?: Cliente;
  treino?: Treino;
  exercicios?: Exercicio[];
  series?: { [exercicioId: number]: Serie[] };
  avaliacoes?: AvaliacaoFisica[];
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

export const ExportPDFButton: React.FC<ExportPDFButtonProps> = ({
  variant = 'menu',
  avaliacao,
  cliente,
  treino,
  exercicios = [],
  series = {},
  avaliacoes = [],
  size = 'medium',
  fullWidth = false
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [loading, setLoading] = useState<string | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (variant === 'menu') {
      setAnchorEl(event.currentTarget);
    } else {
      handleExport(variant);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleExport = async (type: string) => {
    if (!cliente) {
      console.error('Cliente é obrigatório para exportação');
      return;
    }

    setLoading(type);
    handleClose();

    try {
      switch (type) {
        case 'avaliacao':
          if (avaliacao) {
            await PrintService.gerarRelatorioAvaliacaoFisica(avaliacao, cliente);
          }
          break;

        case 'treino':
          if (treino) {
            await PrintService.gerarFichaTreino(treino, cliente, exercicios, series);
          }
          break;

        case 'evolucao':
          if (avaliacoes.length > 0) {
            await PrintService.gerarRelatorioEvolucao(cliente, avaliacoes);
          }
          break;
      }
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
    } finally {
      setLoading(null);
    }
  };

  // Botão único para tipo específico
  if (variant !== 'menu') {
    const getButtonConfig = () => {
      switch (variant) {
        case 'avaliacao':
          return {
            icon: <AssessmentIcon />,
            text: 'Exportar Avaliação',
            disabled: !avaliacao || !cliente
          };
        case 'treino':
          return {
            icon: <FitnessCenterIcon />,
            text: 'Exportar Treino',
            disabled: !treino || !cliente
          };
        case 'evolucao':
          return {
            icon: <TrendingUpIcon />,
            text: 'Relatório Evolução',
            disabled: !cliente || avaliacoes.length < 2
          };
        default:
          return {
            icon: <PdfIcon />,
            text: 'Exportar PDF',
            disabled: false
          };
      }
    };

    const config = getButtonConfig();
    const isLoading = loading === variant;

    return (
      <Tooltip title={config.disabled ? 'Dados insuficientes para exportação' : 'Gerar relatório em PDF'}>
        <span>
          <Button
            variant="contained"
            size={size}
            fullWidth={fullWidth}
            disabled={config.disabled || isLoading}
            onClick={handleClick}
            startIcon={isLoading ? <CircularProgress size={16} /> : config.icon}
            sx={{
              bgcolor: colors.energy[500],
              color: colors.white,
              '&:hover': {
                bgcolor: colors.energy[600]
              },
              '&:disabled': {
                bgcolor: colors.professional[200],
                color: colors.professional[400]
              }
            }}
          >
            {isLoading ? 'Gerando...' : config.text}
          </Button>
        </span>
      </Tooltip>
    );
  }

  // Menu dropdown com múltiplas opções
  return (
    <Box>
      <Tooltip title="Exportar relatórios em PDF">
        <Button
          variant="contained"
          size={size}
          fullWidth={fullWidth}
          onClick={handleClick}
          startIcon={<PdfIcon />}
          sx={{
            bgcolor: colors.energy[500],
            color: colors.white,
            '&:hover': {
              bgcolor: colors.energy[600]
            }
          }}
        >
          Exportar PDF
        </Button>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            borderRadius: 3,
            border: `1px solid ${colors.professional[200]}`,
            boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.12)',
            minWidth: 220
          }
        }}
      >
        <MenuItem
          onClick={() => handleExport('avaliacao')}
          disabled={!avaliacao || !cliente || loading === 'avaliacao'}
          sx={{
            py: 1.5,
            '&:hover': {
              bgcolor: colors.energy[50]
            }
          }}
        >
          <ListItemIcon>
            {loading === 'avaliacao' ? (
              <CircularProgress size={20} sx={{ color: colors.energy[500] }} />
            ) : (
              <AssessmentIcon sx={{ color: colors.energy[500] }} />
            )}
          </ListItemIcon>
          <ListItemText
            primary="Relatório de Avaliação"
            secondary="Avaliação física completa"
            primaryTypographyProps={{
              fontWeight: 500,
              color: colors.professional[700]
            }}
            secondaryTypographyProps={{
              fontSize: '0.75rem',
              color: colors.professional[500]
            }}
          />
        </MenuItem>

        <MenuItem
          onClick={() => handleExport('treino')}
          disabled={!treino || !cliente || loading === 'treino'}
          sx={{
            py: 1.5,
            '&:hover': {
              bgcolor: colors.health[50]
            }
          }}
        >
          <ListItemIcon>
            {loading === 'treino' ? (
              <CircularProgress size={20} sx={{ color: colors.health[500] }} />
            ) : (
              <FitnessCenterIcon sx={{ color: colors.health[500] }} />
            )}
          </ListItemIcon>
          <ListItemText
            primary="Ficha de Treino"
            secondary="Treino para impressão"
            primaryTypographyProps={{
              fontWeight: 500,
              color: colors.professional[700]
            }}
            secondaryTypographyProps={{
              fontSize: '0.75rem',
              color: colors.professional[500]
            }}
          />
        </MenuItem>

        <MenuItem
          onClick={() => handleExport('evolucao')}
          disabled={!cliente || avaliacoes.length < 2 || loading === 'evolucao'}
          sx={{
            py: 1.5,
            '&:hover': {
              bgcolor: colors.professional[50]
            }
          }}
        >
          <ListItemIcon>
            {loading === 'evolucao' ? (
              <CircularProgress size={20} sx={{ color: colors.professional[500] }} />
            ) : (
              <TrendingUpIcon sx={{ color: colors.professional[500] }} />
            )}
          </ListItemIcon>
          <ListItemText
            primary="Relatório de Evolução"
            secondary={`${avaliacoes.length} avaliações disponíveis`}
            primaryTypographyProps={{
              fontWeight: 500,
              color: colors.professional[700]
            }}
            secondaryTypographyProps={{
              fontSize: '0.75rem',
              color: colors.professional[500]
            }}
          />
        </MenuItem>
      </Menu>
    </Box>
  );
};

// ============================================================================
// EXPORT BUTTON VARIANTS - BOTÕES ESPECÍFICOS
// ============================================================================

export const ExportAvaliacaoButton: React.FC<{
  avaliacao: AvaliacaoFisica;
  cliente: Cliente;
  size?: 'small' | 'medium' | 'large';
}> = ({ avaliacao, cliente, size = 'medium' }) => (
  <ExportPDFButton
    variant="avaliacao"
    avaliacao={avaliacao}
    cliente={cliente}
    size={size}
  />
);

export const ExportTreinoButton: React.FC<{
  treino: Treino;
  cliente: Cliente;
  exercicios: Exercicio[];
  series: { [exercicioId: number]: Serie[] };
  size?: 'small' | 'medium' | 'large';
}> = ({ treino, cliente, exercicios, series, size = 'medium' }) => (
  <ExportPDFButton
    variant="treino"
    treino={treino}
    cliente={cliente}
    exercicios={exercicios}
    series={series}
    size={size}
  />
);

export const ExportEvolucaoButton: React.FC<{
  cliente: Cliente;
  avaliacoes: AvaliacaoFisica[];
  size?: 'small' | 'medium' | 'large';
}> = ({ cliente, avaliacoes, size = 'medium' }) => (
  <ExportPDFButton
    variant="evolucao"
    cliente={cliente}
    avaliacoes={avaliacoes}
    size={size}
  />
);

export default ExportPDFButton;
