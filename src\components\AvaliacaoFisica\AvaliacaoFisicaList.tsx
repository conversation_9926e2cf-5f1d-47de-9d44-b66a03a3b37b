import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Typography,
  Avatar,
  Chip,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Button,
  Stack,
  Grid
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  FitnessCenter as FitnessCenterIcon,
  Scale as ScaleIcon,
  Height as HeightIcon,
  Calculate as CalculateIcon
} from '@mui/icons-material';
import { useAvaliacaoFisica } from '../../contexts/AvaliacaoFisicaContext';
import { useCliente } from '../../contexts/ClienteContext';
import { AvaliacaoFisica } from '../../models/AvaliacaoFisica';
import { formatarData } from '../../utils/formatadores';
import { TableSkeleton, CardSkeleton } from '../common/LoadingSkeletons';
import { colors } from '../../styles/colors';

interface AvaliacaoFisicaListProps {
  avaliacoes: AvaliacaoFisica[];
  carregando: boolean;
  onEditar: (avaliacao: AvaliacaoFisica) => void;
  onVisualizar: (avaliacao: AvaliacaoFisica) => void;
  onExcluir: (id: number) => Promise<boolean>;
  onBuscar: (termo: string) => Promise<void>;
}

const AvaliacaoFisicaList: React.FC<AvaliacaoFisicaListProps> = ({
  avaliacoes,
  carregando,
  onEditar,
  onVisualizar,
  onExcluir,
  onBuscar
}) => {
  const {
    erro,
    carregarAvaliacoes,
    buscarAvaliacoes
  } = useAvaliacaoFisica();
  const { clientes } = useCliente();
  
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [termoBusca, setTermoBusca] = useState('');
  const [avaliacaoParaExcluir, setAvaliacaoParaExcluir] = useState<AvaliacaoFisica | null>(null);

  useEffect(() => {
    carregarAvaliacoes();
  }, [carregarAvaliacoes]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleBuscar = (event: React.ChangeEvent<HTMLInputElement>) => {
    const termo = event.target.value;
    setTermoBusca(termo);
    setPage(0);
    if (termo) {
      buscarAvaliacoes(termo);
    } else {
      carregarAvaliacoes();
    }
  };

  const handleConfirmarExclusao = async () => {
    if (avaliacaoParaExcluir) {
      await onExcluir(avaliacaoParaExcluir.id!);
      setAvaliacaoParaExcluir(null);
    }
  };

  const getNomeCliente = (clienteId: number) => {
    const cliente = clientes.find(c => c.id === clienteId);
    return cliente ? cliente.nome : 'Cliente não encontrado';
  };

  const calcularIMC = (peso: number, altura: number) => {
    if (peso && altura) {
      const alturaMetros = altura / 100;
      return (peso / (alturaMetros * alturaMetros)).toFixed(2);
    }
    return '-';
  };

  const classificarIMC = (imc: number) => {
    if (imc < 18.5) return 'Abaixo do peso';
    if (imc < 25) return 'Peso normal';
    if (imc < 30) return 'Sobrepeso';
    if (imc < 35) return 'Obesidade Grau I';
    if (imc < 40) return 'Obesidade Grau II';
    return 'Obesidade Grau III';
  };

  // Função para obter a cor do IMC baseada na classificação
  const getCorIMC = (imc: number): string => {
    if (imc < 18.5) return '#FFC107'; // Amarelo - Abaixo do Peso
    if (imc < 25) return '#4CAF50'; // Verde - Peso Normal
    if (imc < 30) return '#FF9800'; // Laranja - Sobrepeso
    if (imc < 35) return '#F44336'; // Vermelho claro - Obesidade Grau I
    if (imc < 40) return '#E91E63'; // Rosa - Obesidade Grau II
    return '#9C27B0'; // Roxo - Obesidade Grau III
  };

  if (carregando) {
    return (
      <Box>
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Buscar avaliações..."
            disabled
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: colors.professional[300] }} />
                </InputAdornment>
              ),
              sx: {
                borderRadius: 3,
                bgcolor: colors.professional[50]
              }
            }}
            size="small"
          />
        </Box>

        {/* Desktop skeleton */}
        <Box sx={{ display: { xs: 'none', md: 'block' } }}>
          <TableSkeleton rows={10} animated={true} />
        </Box>

        {/* Mobile skeleton */}
        <Box sx={{ display: { xs: 'block', md: 'none' } }}>
          <CardSkeleton rows={4} animated={true} />
        </Box>
      </Box>
    );
  }

  if (erro) {
    return (
      <Typography color="error" align="center" sx={{ color: '#FF4D4D' }}>
        Erro ao carregar avaliações: {erro}
      </Typography>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Buscar avaliações..."
          value={termoBusca}
          onChange={handleBuscar}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: colors.sea }} />
              </InputAdornment>
            ),
            sx: {
              borderRadius: 3,
              '& fieldset': {
                borderColor: colors.sea,
              },
              '&:hover fieldset': {
                borderColor: colors.sea,
              },
              '&.Mui-focused fieldset': {
                borderColor: colors.sea,
              }
            }
          }}
          size="small"
        />
      </Box>

      <TableContainer component={Paper} sx={{ 
        borderRadius: { xs: 3, md: 4 },
        border: `1px solid ${colors.sea}`,
        overflow: 'hidden',
        bgcolor: colors.cloud,
        boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.05)',
        display: { xs: 'none', md: 'block' } // Oculta a tabela em dispositivos móveis
      }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: `${colors.sea}0A` }}>
              <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Data</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Cliente</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Peso (kg)</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Altura (cm)</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>IMC</TableCell>
              <TableCell sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>% Gordura</TableCell>
              <TableCell align="center" sx={{ fontWeight: 600, py: 2, color: colors.ocean }}>Ações</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {avaliacoes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 5 }}>
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column', 
                    alignItems: 'center',
                    p: 3
                  }}>
                    <Box sx={{ 
                      bgcolor: `${colors.sea}1A`, 
                      p: 2, 
                      borderRadius: '50%',
                      mb: 2
                    }}>
                      <FitnessCenterIcon sx={{ fontSize: 40, color: colors.sea }} />
                    </Box>
                    <Typography variant="h6" sx={{ mb: 1, fontWeight: 500, color: colors.ocean }}>
                      Nenhuma avaliação encontrada
                    </Typography>
                    <Typography variant="body2" sx={{ color: `${colors.ocean}99` }}>
                      Tente uma nova busca ou adicione uma nova avaliação
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              avaliacoes
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((avaliacao) => {
                  const imc = calcularIMC(avaliacao.peso, avaliacao.altura);
                  const corIMC = getCorIMC(Number(imc));
                  const nomeCliente = getNomeCliente(avaliacao.cliente_id);
                  
                  return (
                    <TableRow 
                      key={avaliacao.id}
                      sx={{ 
                        '&:hover': { 
                          bgcolor: `${colors.sea}1A` 
                        }
                      }}
                    >
                      <TableCell>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Avatar 
                            sx={{ 
                              width: 32, 
                              height: 32, 
                              bgcolor: `${colors.sea}1A`,
                              color: colors.sea,
                              fontSize: '0.8rem'
                            }}
                          >
                            {formatarData(avaliacao.data_avaliacao).split('/')[0]}
                          </Avatar>
                          <Typography sx={{ color: colors.ocean }}>
                            {formatarData(avaliacao.data_avaliacao)}
                          </Typography>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Typography sx={{ color: colors.ocean, fontWeight: 500 }}>
                          {nomeCliente}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={<ScaleIcon sx={{ fontSize: 16 }} />}
                          label={`${avaliacao.peso} kg`}
                          size="small"
                          sx={{ 
                            bgcolor: `${colors.sea}0A`,
                            color: colors.sea,
                            '.MuiChip-icon': {
                              color: colors.sea
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={<HeightIcon sx={{ fontSize: 16 }} />}
                          label={`${avaliacao.altura} cm`}
                          size="small"
                          sx={{ 
                            bgcolor: `${colors.sea}0A`,
                            color: colors.sea,
                            '.MuiChip-icon': {
                              color: colors.sea
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={<CalculateIcon sx={{ fontSize: 16 }} />}
                          label={imc}
                          size="small"
                          sx={{ 
                            bgcolor: `${corIMC}1A`,
                            color: corIMC,
                            '.MuiChip-icon': {
                              color: corIMC
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={<FitnessCenterIcon sx={{ fontSize: 16 }} />}
                          label={`${avaliacao.dobras_cutaneas?.percentual_gordura || 0}%`}
                          size="small"
                          sx={{ 
                            bgcolor: `${colors.sea}0A`,
                            color: colors.ocean,
                            border: `1px solid ${colors.sea}`,
                            '& .MuiChip-icon': {
                              color: colors.sea
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="Visualizar">
                          <IconButton 
                            onClick={() => onVisualizar(avaliacao)}
                            size="small"
                            sx={{ 
                              color: colors.sea,
                              '&:hover': { 
                                bgcolor: `${colors.sea}1A` 
                              }
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Editar">
                          <IconButton 
                            onClick={() => onEditar(avaliacao)}
                            size="small"
                            sx={{ 
                              color: colors.sea,
                              '&:hover': { 
                                bgcolor: `${colors.sea}1A` 
                              }
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Excluir">
                          <IconButton 
                            onClick={() => setAvaliacaoParaExcluir(avaliacao)}
                            size="small"
                            sx={{ 
                              color: '#FF4D4D',
                              '&:hover': { 
                                bgcolor: 'rgba(255, 77, 77, 0.1)' 
                              }
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  );
                })
            )}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={avaliacoes.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Itens por página:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
          sx={{
            borderTop: `1px solid ${colors.sea}`,
            '.MuiTablePagination-select': {
              color: colors.ocean
            },
            '.MuiTablePagination-displayedRows': {
              color: colors.ocean
            },
            '.MuiTablePagination-selectLabel': {
              color: colors.ocean
            }
          }}
        />
      </TableContainer>

      {/* Versão para dispositivos móveis */}
      <Box sx={{ display: { xs: 'block', md: 'none' } }}>
        {avaliacoes.length === 0 ? (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center',
            p: 3,
            bgcolor: colors.cloud,
            borderRadius: 3,
            border: `1px solid ${colors.sea}`
          }}>
            <Box sx={{ 
              bgcolor: `${colors.sea}1A`, 
              p: 2, 
              borderRadius: '50%',
              mb: 2
            }}>
              <FitnessCenterIcon sx={{ fontSize: 40, color: colors.sea }} />
            </Box>
            <Typography variant="h6" sx={{ mb: 1, fontWeight: 500, color: colors.ocean }}>
              Nenhuma avaliação encontrada
            </Typography>
            <Typography variant="body2" sx={{ color: `${colors.ocean}99`, textAlign: 'center' }}>
              Tente uma nova busca ou adicione uma nova avaliação
            </Typography>
          </Box>
        ) : (
          <>
            {avaliacoes
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((avaliacao) => {
                const imc = calcularIMC(avaliacao.peso, avaliacao.altura);
                const corIMC = getCorIMC(Number(imc));
                const nomeCliente = getNomeCliente(avaliacao.cliente_id);
                
                return (
                  <Paper
                    key={avaliacao.id}
                    elevation={0}
                    sx={{ 
                      mb: 2, 
                      p: 2, 
                      borderRadius: 3,
                      border: `1px solid ${colors.sea}`,
                      '&:hover': { 
                        borderColor: colors.sea,
                        boxShadow: `0px 4px 8px ${colors.sea}19`
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Avatar 
                          sx={{ 
                            width: 32, 
                            height: 32, 
                            bgcolor: `${colors.sea}1A`,
                            color: colors.sea,
                            fontSize: '0.8rem'
                          }}
                        >
                          {formatarData(avaliacao.data_avaliacao).split('/')[0]}
                        </Avatar>
                        <Box>
                          <Typography sx={{ color: colors.ocean, fontWeight: 500, fontSize: '0.9rem' }}>
                            {formatarData(avaliacao.data_avaliacao)}
                          </Typography>
                          <Typography sx={{ color: `${colors.ocean}99`, fontSize: '0.8rem' }}>
                            {nomeCliente}
                          </Typography>
                        </Box>
                      </Stack>
                      <Box>
                        <Tooltip title="Visualizar">
                          <IconButton 
                            onClick={() => onVisualizar(avaliacao)}
                            size="small"
                            sx={{ 
                              color: colors.sea,
                              '&:hover': { 
                                bgcolor: `${colors.sea}1A` 
                              }
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Editar">
                          <IconButton 
                            onClick={() => onEditar(avaliacao)}
                            size="small"
                            sx={{ 
                              color: colors.sea,
                              '&:hover': { 
                                bgcolor: `${colors.sea}1A` 
                              }
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                    
                    <Grid container spacing={1}>
                      <Grid item xs={6}>
                        <Chip 
                          icon={<ScaleIcon sx={{ fontSize: 14 }} />}
                          label={`${avaliacao.peso} kg`}
                          size="small"
                          sx={{ 
                            bgcolor: `${colors.sea}0A`,
                            color: colors.sea,
                            '.MuiChip-icon': {
                              color: colors.sea
                            },
                            fontSize: '0.75rem',
                            height: 24
                          }}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Chip 
                          icon={<HeightIcon sx={{ fontSize: 14 }} />}
                          label={`${avaliacao.altura} cm`}
                          size="small"
                          sx={{ 
                            bgcolor: `${colors.sea}0A`,
                            color: colors.sea,
                            '.MuiChip-icon': {
                              color: colors.sea
                            },
                            fontSize: '0.75rem',
                            height: 24
                          }}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Chip 
                          icon={<CalculateIcon sx={{ fontSize: 14 }} />}
                          label={`IMC: ${imc}`}
                          size="small"
                          sx={{ 
                            bgcolor: `${corIMC}1A`,
                            color: corIMC,
                            '.MuiChip-icon': {
                              color: corIMC
                            },
                            fontSize: '0.75rem',
                            height: 24
                          }}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Chip 
                          icon={<FitnessCenterIcon sx={{ fontSize: 14 }} />}
                          label={`${avaliacao.dobras_cutaneas?.percentual_gordura || 0}%`}
                          size="small"
                          sx={{ 
                            bgcolor: `${colors.sea}0A`,
                            color: colors.ocean,
                            border: `1px solid ${colors.sea}`,
                            '& .MuiChip-icon': {
                              color: colors.sea
                            }
                          }}
                        />
                      </Grid>
                    </Grid>
                  </Paper>
                );
              })}
              
            <Box sx={{ 
              display: 'flex', 
              justifyContent: 'center', 
              mt: 2,
              bgcolor: colors.cloud,
              borderRadius: 3,
              border: `1px solid ${colors.sea}`,
              p: 1
            }}>
              <TablePagination
                component="div"
                count={avaliacoes.length}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Por página:"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
                sx={{
                  '.MuiTablePagination-select': {
                    color: colors.ocean
                  },
                  '.MuiTablePagination-displayedRows': {
                    color: colors.ocean
                  },
                  '.MuiTablePagination-selectLabel': {
                    color: colors.ocean
                  }
                }}
              />
            </Box>
          </>
        )}
      </Box>

      <Dialog
        open={!!avaliacaoParaExcluir}
        onClose={() => setAvaliacaoParaExcluir(null)}
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
          }
        }}
      >
        <DialogTitle sx={{ 
          color: colors.ocean,
          fontWeight: 600,
          borderBottom: `1px solid ${colors.sea}`,
          bgcolor: `${colors.sea}0A`,
          p: 3
        }}>
          Confirmar Exclusão
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <DialogContentText sx={{ color: `${colors.ocean}99` }}>
            Tem certeza que deseja excluir esta avaliação? Esta ação não pode ser desfeita.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button 
            onClick={() => setAvaliacaoParaExcluir(null)}
            sx={{ 
              color: `${colors.ocean}99`,
              '&:hover': {
                bgcolor: `${colors.ocean}0A`,
                color: colors.ocean
              }
            }}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleConfirmarExclusao}
            variant="contained"
            sx={{ 
              bgcolor: '#FF4D4D',
              '&:hover': {
                bgcolor: '#E60000'
              }
            }}
          >
            Excluir
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AvaliacaoFisicaList; 